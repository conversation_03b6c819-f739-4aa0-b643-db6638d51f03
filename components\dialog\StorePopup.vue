<template>
  <Teleport to="body">
    <Transition
      name="modal"
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-150 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-xs p-4"
        @click.self="handleCancel"
        @keydown.esc="handleCancel"
        tabindex="-1"
        role="dialog"
        aria-modal="true"
        aria-labelledby="store-popup-title"
        aria-describedby="store-popup-description"
      >
        <div
          class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full transform transition-all duration-200 ease-out"
          :class="{ 'animate-popup': isVisible }"
          @click.stop
        >
          <div class="flex items-center justify-center">
            <div
              class="w-12 h-12 rounded-full flex items-center justify-center mb-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6 text-yellow-400"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
                />
              </svg>
            </div>
            <h2
              id="store-popup-title"
              class="text-xl font-semibold text-center text-gray-900 mb-4"
            >
              Xác nhận cửa hàng
            </h2>
          </div>

          <div
            id="store-popup-description"
            class="text-center text-gray-600 mb-6 leading-relaxed"
          >
            <template v-if="isLoading">
              <div class="flex items-center justify-center space-x-2">
                <div
                  class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"
                ></div>
                <span>Đang tải thông tin cửa hàng...</span>
              </div>
            </template>
            <template v-else-if="dataSubStore?.name">
              Bạn đang thao tác tại cửa hàng
              <span class="font-semibold text-primary">{{
                dataSubStore.name
              }}</span
              >.
              <br />
              Bạn có muốn tiếp tục thao tác tại cửa hàng này không?
            </template>
            <template v-else>
              <div class="text-red-500">
                Không thể tải thông tin cửa hàng. Vui lòng thử lại.
              </div>
            </template>
          </div>

          <!-- ✅ Action buttons with better styling -->
          <div class="flex items-center justify-center space-x-3 w-full">
            <button
              @click="handleCancel"
              :disabled="isLoading"
              class="flex-1 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-hidden focus:ring-2 focus:ring-gray-300"
              type="button"
            >
              Không đồng ý
            </button>
            <button
              @click="handleConfirm"
              :disabled="isLoading || !dataSubStore?.name"
              class="flex-1 px-4 py-2.5 bg-primary hover:bg-primary-dark text-white rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-hidden focus:ring-2 focus:ring-primary/50"
              type="button"
            >
              Đồng ý
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const isLoading = ref(true);
const dataSubStore = ref(null);
const error = ref(null);

const { subStoreId } = useTabContext();
const { getDetailStoreV2 } = useStore();

const handleConfirm = async () => {
  if (isLoading.value || !dataSubStore.value?.name) return;

  try {
    emit("confirm");
    isVisible.value = false;
  } catch (err) {
    console.error("Error confirming store selection:", err);
  }
};

const handleCancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const fetchStoreData = async () => {
  if (!subStoreId.value) {
    error.value = "Không tìm thấy ID cửa hàng";
    isLoading.value = false;
    return;
  }

  try {
    isLoading.value = true;
    error.value = null;

    const storeData = await getDetailStoreV2(subStoreId.value);
    dataSubStore.value = storeData;
  } catch (err) {
    console.error("Error fetching store data:", err);
    error.value = "Không thể tải thông tin cửa hàng";
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchStoreData();
});

onUnmounted(() => {
  isVisible.value = false;
});
</script>

<style scoped>
.animate-popup {
  animation: popupAnimation 0.2s ease-out forwards;
}

@keyframes popupAnimation {
  0% {
    transform: scale(0.95) translateY(-10px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@media (max-width: 640px) {
  .max-w-md {
    max-width: calc(100vw - 2rem);
    margin: 1rem;
  }

  .p-6 {
    padding: 1.25rem;
  }
}
</style>
