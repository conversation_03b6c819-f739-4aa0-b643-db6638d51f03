<template>
  <div class="bg-white p-2 rounded">
    <h3 class="text-lg font-semibold text-primary">Thông tin đơn hàng</h3>
    <div class="grid grid-cols-2">
      <div class="col-span-1">
        <div>
          <span>Mã đơn: </span>
          <span>{{ order?.id }}</span>
        </div>
        <div>
          <span>Ngày tạo đơn: </span>
          <span>{{ formatTimestampV7(order?.order?.orderDate) }}</span>
        </div>
        <div>
          <span>Trạng thái:</span>
          <span :class="getOrderStatusClass(order?.status)">{{
            order?.statusDescription
          }}</span>
        </div>
        <div>
          <span>Fullfillment:</span>
          <span :class="getFFMStatusClass(order?.order?.fulfillmentStatus)">{{
            getFFMStatusText(order?.order?.fulfillmentStatus)
          }}</span>
        </div>
      </div>
      <!-- cột 2 -->
      <div class="col-span-1">
        <div>
          <span>Nhân viên tạo đơn: </span>
          <span>{{ employeeCreate }}</span>
        </div>
        <div>
          <span>Nhân viên tư vấn: </span>
          <span>{{ employeeSale }}</span>
        </div>
        <div>
          <span>Trạng thái thanh toán: </span>
          <span
            :class="getPaymentStatusClassV2(order?.financialStatusDescription)"
            >{{ order?.financialStatusDescription }}</span
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);
// Import utilities
import {
  getOrderStatusClass,
  getPaymentStatusClassV2,
  getFFMStatusClass,
  getFFMStatusText,
} from "~/utils/statusHelpers";
const { searchEmployes } = useOrder();
const getEmployee = async (idEmployee) => {
  const data = {
    keyword: idEmployee,
    positionShortName: "",
  };
  try {
    const response = await searchEmployes(data);
    return response;
  } catch (error) {
    throw error;
  }
};
const employeeCreate = ref();
const employeeSale = ref();
onMounted(async () => {
  const res = await getEmployee(props.order?.order?.createdBy);
  employeeCreate.value = res[0]?.name;
  const res1 = await getEmployee(props.order?.order?.salePartyId);
  employeeSale.value = res1[0]?.name;
});
</script>
