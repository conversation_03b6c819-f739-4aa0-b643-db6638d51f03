<template>
  <div class="p-2 ">
    <div class="text-primary-light font-semibold text-sm py-2 border-t  ">Sản phẩm n<PERSON>i bật</div>
    <div v-if="isLoading">Loading...</div>
    <div v-else class="flex gap-2 overflow-x-auto flex-nowrap ">
      <div
        class="items-center pb-2"
        v-for="product in products"
        :key="product.id"
      >
        <ProductCard :product="product" />
      </div>
    </div>
    <div class="h-[60px] block md:hidden"></div>
  </div>
</template>
<script setup>
const productStore = useProductStore();
const products = computed(() => productStore.featuredProducts);
const isLoading = computed(() => productStore.isLoading);

</script>
