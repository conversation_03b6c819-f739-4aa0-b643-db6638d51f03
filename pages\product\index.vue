<template>
  <div class="h-[calc(100vh-56px)] mx-2">
    <!-- Desktop Layout -->
    <div class="hidden md:flex h-full gap-2">
      <!-- Left Sidebar - Categories (Fixed Width) -->
      <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
        <!-- Category Header -->

        <!-- Category Search -->
        <div class="p-3 border-b border-gray-200">
          <div class="relative">
            <input
              type="text"
              v-model="categorySearchKeyword"
              placeholder="Tìm kiếm danh mục..."
              class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 absolute left-2.5 top-2.5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>

        <!-- Category List -->
        <div class="flex-1 overflow-y-auto">
          <div class="p-2">
            <div
              v-for="category in filteredCategories"
              :key="category?.id"
              class="mb-1"
            >
              <div
                class="cursor-pointer px-3 py-2.5 text-sm transition-all duration-200 hover:bg-blue-50 hover:text-primary"
                @click="handleSetCategory(category)"
                :class="
                  selectedCategory === category?.id
                    ? 'text-primary border-primary border-l-2'
                    : 'text-gray-700 hover:bg-blue-50'
                "
              >
                <div class="flex items-center justify-between">
                  <span class="truncate">{{ category?.title }}</span>
                  <span
                    v-if="selectedCategory === category?.id"
                    class="ml-2 text-xs opacity-75"
                  >
                    ✓
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Category Stats -->
        <div class="p-[22px] border-t border-gray-200 bg-gray-50">
          <div class="text-xs text-gray-500 text-center">
            {{ filteredCategories.length }} danh mục
          </div>
        </div>
      </div>

      <!-- Right Content Area -->
      <div class="flex-1 flex flex-col gap-2  ">
        <!-- Search Bar -->
        <div class="border-b border-gray-200 bg-white p-[10px]">
          <ProductSearch
            :isLoading="isLoading"
            @search="handleSetData"
            @clearQuery="handleClearQuery"
          />
        </div>

        <!-- Product Table -->
        <div class="flex-1 bg-white overflow-hidden">
          <TableManagerProduct
            :ListProduct="paginatedProducts"
            :isLoading="isLoading"
            :dataUnit="dataUnit"
            :dataCategories="dataCategories"
            :currentPage="pagination.currentPage"
            :totalPages="pagination.totalPages"
            :totalItems="pagination.totalItems"
            :itemsPerPage="pagination.itemsPerPage"
            @updateProduct="handleUpdateProduct"
            @viewDetail="handleViewDetail"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- Mobile Layout -->
    <div class="md:hidden h-full flex flex-col">
      <!-- Mobile Header with Category Toggle -->
      <div class="bg-white border-b border-gray-200 p-4">
        <div class="flex items-center justify-between mb-3">
          <h1 class="text-lg font-semibold text-gray-900">Quản lý sản phẩm</h1>
          <button
            @click="showMobileCategories = !showMobileCategories"
            class="flex items-center gap-2 px-3 py-2 text-sm bg-primary text-white rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            Danh mục
          </button>
        </div>

        <ProductSearch
          :isLoading="isLoading"
          @search="handleSetData"
          @clearQuery="handleClearQuery"
        />
      </div>

      <!-- Mobile Category Dropdown -->
      <div
        v-if="showMobileCategories"
        class="bg-white border-b border-gray-200 max-h-48 overflow-y-auto"
      >
        <div class="p-2">
          <div
            v-for="category in filteredCategories"
            :key="category?.id"
            class="mb-1"
          >
            <div
              class="cursor-pointer px-3 py-2 text-sm rounded-md"
              @click="handleSetCategoryMobile(category)"
              :class="
                selectedCategory === category?.id
                  ? 'bg-primary text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              "
            >
              {{ category?.title }}
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile Product List -->
      <div class="flex-1 bg-white overflow-hidden">
        <TableManagerProduct
          :ListProduct="paginatedProducts"
          :isLoading="isLoading"
          :dataUnit="dataUnit"
          :dataCategories="dataCategories"
          :currentPage="pagination.currentPage"
          :totalPages="pagination.totalPages"
          :totalItems="pagination.totalItems"
          :itemsPerPage="pagination.itemsPerPage"
          @updateProduct="handleUpdateProduct"
          @viewDetail="handleViewDetail"
          @page-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Quản lý sản phẩm",
});
useHead({
  title: "Quản lý sản phẩm",
  meta: [
    {
      name: "description",
      content: "Quản lý sản phẩm",
    },
  ],
});
const { getProduct, getCategory, getProductById } = useProduct();
const options = reactive({
  keyword: "",
  category: "",
  maxResult: 20,
  currentPage: 1,
});
const isLoading = ref(false);
const ListProduct = ref<any>([]);

// Pagination
const pagination = reactive({
  currentPage: 1,
  itemsPerPage: 20,
  totalItems: 0,
  totalPages: 1,
});

// Track if we have more data (for APIs without total count)
const hasMoreData = ref(true);
// Separate function for loading data without resetting page
const loadProducts = async () => {
  try {
    isLoading.value = true;
    const response = (await getProduct(options)) as any;
    ListProduct.value = response?.data || [];
    updatePagination(response);
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

// Search function that resets to page 1
const handleSearch = async () => {
  options.currentPage = 1;
  pagination.currentPage = 1;
  hasMoreData.value = true; // Reset hasMoreData when searching
  await loadProducts();
};
const handleUpdateProduct = async (productId: string) => {
  const index = ListProduct.value.findIndex(
    (product: any) => product?.id === productId
  );

  if (index !== -1) {
    const response = await getProductById(productId);

    ListProduct.value = [
      ...ListProduct.value.slice(0, index),
      response, // gán phần tử mới
      ...ListProduct.value.slice(index + 1),
    ];
  }
};

const selectedCategory = ref();
const handleSetCategory = async (category: any) => {
  if (isLoading.value) return;

  selectedCategory.value = category?.id;
  if (selectedCategory.value !== 99999) {
    options.category = selectedCategory.value;
  } else {
    options.category = "";
  }
  await handleSearch();
};
const handleSetData = async (data: any) => {
  if (isLoading.value) return;

  options.keyword = data?.keyword;
  if (!selectedCategory.value) {
    options.category = data?.category;
  }
  await handleSearch();
};
const handleClearQuery = async (data: any) => {
  if (isLoading.value) return;

  options.keyword = data?.keyword;
  options.category = data?.category;
  await handleSearch();
};
// Computed property for paginated products
const paginatedProducts = computed(() => {
  return ListProduct.value;
});

// Pagination handlers
const handlePageChange = async (newPage: number) => {
  if (newPage < 1 || newPage > pagination.totalPages || isLoading.value) return;

  pagination.currentPage = newPage;
  options.currentPage = newPage;

  await loadProducts();
};

// Update pagination info from API response
const updatePagination = (response: any) => {
  try {
    // Check different possible response structures
    if (response?.pagination) {
      // If API returns pagination metadata
      pagination.totalItems = response.pagination.totalItems || 0;
      pagination.totalPages = response.pagination.totalPages || 1;
      pagination.currentPage =
        response.pagination.currentPage || options.currentPage;
    } else if (response?.total !== undefined) {
      // If API returns total count
      pagination.totalItems = response.total;
      pagination.totalPages = Math.ceil(
        response.total / pagination.itemsPerPage
      );
      pagination.currentPage = options.currentPage;
    } else if (response?.data) {
      // Fallback: estimate from data length (not ideal for real pagination)
      const dataLength = response.data.length;

      // Update hasMoreData based on returned data length
      hasMoreData.value = dataLength >= pagination.itemsPerPage;

      // If we get less than itemsPerPage, we're probably on the last page
      if (dataLength < pagination.itemsPerPage) {
        pagination.totalItems =
          (options.currentPage - 1) * pagination.itemsPerPage + dataLength;
        pagination.totalPages = options.currentPage;
      } else {
        // Estimate - this is not accurate but better than nothing
        pagination.totalItems =
          options.currentPage * pagination.itemsPerPage + 1; // +1 to suggest there might be more
        pagination.totalPages = hasMoreData.value
          ? options.currentPage + 1
          : options.currentPage;
      }
      pagination.currentPage = options.currentPage;
    } else {
      // Last fallback
      pagination.totalItems = 0;
      pagination.totalPages = 1;
      pagination.currentPage = 1;
    }

    // Ensure pagination values are valid
    if (pagination.totalPages < 1) pagination.totalPages = 1;
    if (pagination.currentPage < 1) pagination.currentPage = 1;
    if (pagination.currentPage > pagination.totalPages) {
      pagination.currentPage = pagination.totalPages;
    }
  } catch (error) {
    console.error("Error updating pagination:", error);
    // Reset to safe defaults
    pagination.totalItems = 0;
    pagination.totalPages = 1;
    pagination.currentPage = 1;
  }
};

const listCategories = ref<any>([]);
const dataCategories = ref<any>([]);
const handleGetCategories = async () => {
  try {
    const response = await getCategory("", 1);
    listCategories.value = response;
    dataCategories.value = response;
    const data = [
      {
        title: "Tất cả",
        id: 99999,
      },
    ];
    listCategories.value = [...data, ...listCategories.value];
    selectedCategory.value = 99999;
  } catch (error) {
    throw error;
  }
};
const { getUnits } = useProduct();
const dataUnit = ref<any>([]);
const handleGetUnit = async () => {
  try {
    const response = await getUnits();
    dataUnit.value = response;
    return response;
  } catch (error) {
    throw error;
  }
};

// Category search and filtering
const categorySearchKeyword = ref<string>("");
const showMobileCategories = ref<boolean>(false);

const filteredCategories = computed(() => {
  if (!categorySearchKeyword.value) {
    return listCategories.value;
  }
  return listCategories.value.filter((category: any) =>
    category?.title
      ?.toLowerCase()
      .includes(categorySearchKeyword.value.toLowerCase())
  );
});

// Mobile category handler
const handleSetCategoryMobile = async (category: any) => {
  showMobileCategories.value = false;
  await handleSetCategory(category);
};

// View detail handler
const handleViewDetail = (productId: string) => {
  const route = useRoute();
  navigateTo(
    `/product/detail?productId=${productId}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};

onMounted(async () => {
  await handleGetCategories();
  await handleGetUnit();
  await loadProducts(); // Load initial data
});
</script>
