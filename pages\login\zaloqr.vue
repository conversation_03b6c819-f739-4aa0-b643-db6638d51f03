<template>
  <div
    class="min-h-screen flex items-center justify-center overflow-hidden relative bg-gradient-to-br from-[#d6ecff] via-[#e6f3ff] to-[#f4faff]"
  >
    <div
      class="qr-container bg-white/95 backdrop-blur-lg border border-white/30 rounded-3xl shadow-2xl p-10 max-w-lg w-full mx-4 z-10 animate-[float_6s_ease-in-out_infinite]"
    >
      <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900 mb-1">
          C<PERSON>u hình <PERSON>alo <PERSON>
        </h1>
        <p class="text-gray-700 mb-1 leading-relaxed drop-shadow-sm">
          Chọn cửa hàng và quét mã QR bằng ứng dụng Zalo để cấu hình tài khoản.
        </p>

        <div class="mb-3">
          <label
            for="store-select"
            class="block text-sm font-semibold text-gray-800 mb-1"
            >Chọn C<PERSON></label
          >
          <select
            id="store-select"
            v-model="selectedStore"
            class="w-full p-3 rounded-lg outline-none border"
          >
            <option value="" disabled selected>Chọn một cửa hàng</option>
            <option
              v-for="store in dataStore"
              :value="store?.id"
              :key="store?.id"
            >
              {{ store?.name }}
            </option>
          </select>
        </div>

        <div
          v-if="selectedStore && !isLoginSuccess"
          class="qr-code-wrapper rounded-xl p-4 shadow-md transition-transform hover:scale-[1.05] hover:shadow-lg mb-3 flex justify-center"
        >
          <img
            v-if="!isLoginSuccess"
            :src="qrCode"
            alt="Mã QR Zalo"
            class="w-56 h-56 rounded-lg"
          />
        </div>

        <div
          v-if="isLoading"
          class="w-10 h-10 border-4 border-[#0068ff]/20 border-l-[#0068ff] rounded-full animate-spin mx-auto"
        ></div>

        <p
          :class="[
            'text-base mt-6 font-medium drop-shadow-sm',
            isSuccess ? 'text-green-600 font-semibold' : 'text-gray-600',
          ]"
        >
          {{ status }}
        </p>

        <button
          v-if="selectedStore && !isLoginSuccess"
          @click="handleCreateQRlogin(Date.now())"
          class="mt-3 bg-gradient-to-r from-[#0068ff] to-[#4dabff] text-white px-8 py-3 rounded-full font-semibold transition-all hover:-translate-y-1 hover:shadow-lg hover:from-[#0053cc] hover:to-[#3b8bff]"
        >
          Làm mới mã QR
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
useHead({
  title: "Đăng nhập Zalo",
  meta: [
    {
      name: "description",
      content: "Đăng nhập Zalo",
    },
  ],
});

definePageMeta({
  name: "Đăng nhập Zalo",
});
const selectedStore = ref("");
const status = ref("Vui lòng chọn cửa hàng để tiếp tục...");
const isLoading = ref(false);
const isSuccess = ref(false);
const router = useRouter();
const dataStore = ref([]);
const isLoginSuccess = ref(false);
watch(selectedStore, async (storeId) => {
  if (storeId) {
    status.value = "Đang chờ quét mã QR...";
    setStore(selectedStore.value);
    await router.push({
      query: {
        ...route.query, // giữ lại các query cũ nếu có
        storeId: selectedStore.value,
      },
    });
    const sessionId = Date.now();
    const response = await handleCreateQRlogin(sessionId);
    if (response) {
      clearInterval(intervalId);
      isSuccess.value = true;
      status.value = "Cấu hình tài khoản đăng nhập thành công";
      isLoginSuccess.value = true;
    }
  } else {
    status.value = "Vui lòng chọn cửa hàng để tiếp tục...";
  }
});

const { getStoreV2 } = useStore();
const { setOrgId, setStore } = usePermission();
const route = useRoute();

onMounted(async () => {
  await setOrgId(route.query.orgId);
  dataStore.value = await getStoreV2();
  if (route.query.storeId) {
    selectedStore.value = route.query.storeId;
  }
});
const { getLoginQr, getUrl } = useZca();

const endpoint = ref(""); 
const qrCode = ref("");
const qrCodeTimestamp = ref(0);
let intervalId;
const handleCreateQRlogin = async (sessionId) => {
  isLoginSuccess.value = false;
  isSuccess.value = false;
  clearInterval(intervalId);
  intervalId = setInterval(() => {
    handleGetEndpoint(sessionId);
  }, 500);
  try {
    const response = await getLoginQr(sessionId, route.query?.appId || "");
    console.log("response", response);
    return response;
  } catch (error) {
    console.error("Lỗi tạo mã QR:", error);
  }
};

const handleGetEndpoint = (sessionId) => {
  const response = getUrl();
  endpoint.value = response.endpoint;
  qrCode.value = `${endpoint.value}/Uploads/qr-${sessionId}.png`;
};
onUnmounted(() => {
  clearInterval(intervalId);
});
</script>
