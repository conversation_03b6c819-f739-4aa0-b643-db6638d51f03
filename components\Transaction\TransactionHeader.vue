<template>
  <div class="bg-white border-b border-gray-200 px-4 py-3">
    <!-- Mobile Layout -->
    <div class="md:hidden">
      <div class="flex items-center justify-between mb-3">
        <h1 class="text-lg font-semibold text-gray-900">Quản lý Giao dịch</h1>
        <button
          @click="$emit('add-transaction')"
          class="flex items-center gap-2 px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Thêm
        </button>
      </div>

      <!-- Mobile Stats -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div class="text-sm">
          <span class="text-gray-500">Tổng Giao dịch:</span>
          <span class="font-semibold text-green-600 ml-1">{{
            data?.total
          }}</span>
        </div>
      </div>
    </div>

    <!-- Desktop Layout -->
    <div class="hidden md:flex items-center justify-between">
      <!-- Left: Title and Stats -->
      <div class="flex items-center gap-6">
        <h1 class="text-xl font-semibold text-gray-900">Quản lý thu chi</h1>
        <div class="flex items-center gap-4">
          <div class="text-sm">
            <span class="text-gray-500">Tổng Giao dịch:</span>
            <span class="font-semibold text-green-600 ml-1">{{
              data?.total
            }}</span>
          </div>
        </div>
      </div>

      <!-- Right: Actions -->
      <div class="flex items-center gap-3">
        <!-- Date Range Picker -->
        <!-- <div class="flex items-center gap-2">
          <input
            type="date"
            v-model="dateRange.start"
            class="px-3 py-2 border border-gray-300 rounded-lg text-sm outline-none"
          />
          <span class="text-gray-400">-</span>
          <input
            type="date"
            v-model="dateRange.end"
            class="px-3 py-2 border border-gray-300 rounded-lg text-sm outline-none"
          />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["data"]);

const emit = defineEmits(["export", "add-transaction", "date-change"]);
</script>
