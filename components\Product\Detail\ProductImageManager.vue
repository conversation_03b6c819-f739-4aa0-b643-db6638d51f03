<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-gray-200 lg:h-screen-50 lg:overflow-y-auto"
  >
    <!-- Header -->
    <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-purple-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Hình ảnh sản phẩm</h2>
          <p class="text-sm text-gray-500">
            Quản lý hình ảnh và video sản phẩm
          </p>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-4 sm:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
      <!-- Featured Image -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Hình ảnh chính
        </label>

        <div class="flex flex-col items-center">
          <!-- Current Image -->
          <div v-if="featuredImageUrl" class="mb-4">
            <div class="relative group">
              <img
                :src="featuredImageUrl"
                alt="Product Image"
                class="w-32 h-32 sm:w-48 sm:h-48 object-cover rounded-lg border border-gray-200 shadow-sm"
              />
              <div
                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center"
              >
                <div
                  class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2"
                >
                  <button
                    @click="handleChangeImage"
                    class="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors duration-200"
                    title="Thay đổi hình ảnh"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-gray-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                  <button
                    @click="handleRemoveImage"
                    class="p-2 bg-white rounded-lg shadow-md hover:bg-red-50 transition-colors duration-200"
                    title="Xóa hình ảnh"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-red-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Upload Area -->
          <div v-else class="w-full">
            <div
              @click="handleUploadClick"
              @dragover.prevent
              @drop.prevent="handleDrop"
              class="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-8 text-center hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-pointer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2 sm:mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <p
                class="text-sm sm:text-base text-gray-600 font-medium mb-1 sm:mb-2"
              >
                Tải lên hình ảnh sản phẩm
              </p>
              <p class="text-xs sm:text-sm text-gray-500">
                Kéo thả hoặc click để chọn file
              </p>
              <p class="text-xs text-gray-400 mt-1 sm:mt-2">
                PNG, JPG, GIF tối đa 10MB
              </p>
            </div>
          </div>

          <!-- Upload Button -->
          <button
            v-if="!featuredImageUrl"
            @click="handleUploadClick"
            class="mt-2 sm:mt-4 px-3 py-2 sm:px-4 sm:py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200 font-medium text-sm sm:text-base"
          >
            Chọn hình ảnh
          </button>
        </div>
      </div>

      <!-- Additional Images -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Hình ảnh bổ sung
        </label>
        <div class="grid grid-cols-3 gap-3">
          <!-- Add more image slots here -->
          <div
            v-for="n in 3"
            :key="n"
            class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- Video Section -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Video sản phẩm
        </label>
        <div
          class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-10 w-10 text-gray-400 mx-auto mb-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
          <p class="text-gray-600 font-medium mb-1">Thêm video sản phẩm</p>
          <p class="text-sm text-gray-500">MP4, MOV, AVI tối đa 50MB</p>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      class="hidden"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  product: any;
}

const props = defineProps<Props>();
const emit = defineEmits(["update"]);

const fileInput = ref<HTMLInputElement>();
const featuredImageUrl = ref<string>("");

// Watch for product changes
watch(
  () => props.product,
  (newVal) => {
    if (newVal?.featuredImage) {
      featuredImageUrl.value = newVal.featuredImage;
    }
  },
  { immediate: true }
);

// File upload handlers
const handleUploadClick = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    handleFileUpload(file);
  }
};

const handleDrop = (event: DragEvent) => {
  const file = event.dataTransfer?.files[0];
  if (file && file.type.startsWith("image/")) {
    handleFileUpload(file);
  }
};

const handleFileUpload = async (file: File) => {
  // TODO: Implement file upload logic
  console.log("Uploading file:", file);

  // For now, create a preview URL
  const previewUrl = URL.createObjectURL(file);
  featuredImageUrl.value = previewUrl;

  emit("update");
};

const handleChangeImage = () => {
  fileInput.value?.click();
};

const handleRemoveImage = () => {
  featuredImageUrl.value = "";
  emit("update");
};
</script>
