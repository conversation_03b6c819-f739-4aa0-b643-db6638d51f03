export default function useCheckin() {
  const $sdk = useNuxtApp().$sdk;
  const getStore = async () => {
    try {
      const response = await $sdk.product.getStores("pos");
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getStoreV2 = async () => {
    try {
      const response = await $sdk.product.getStores("");
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getDetailStore = async () => {
    try {
      const response = await $sdk.product.getDetailStores();
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getStoreChannelIdsByEmployeeId = async (employeeId: string) => {
    try {
      const response = await $sdk.user.getStoreChannelIdsByEmployeeId(
        employeeId
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getDetailStoreV2 = async (storeId: any) => {
    try {
      const response = await $sdk.product.getDetailStoresV2(storeId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    getStore,
    getDetailStore,
    getStoreChannelIdsByEmployeeId,
    getDetailStoreV2,
    getStoreV2
  };
}
