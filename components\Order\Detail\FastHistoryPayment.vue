<template>
  <div class="bg-white p-2 rounded">
    <h3 class="text-lg font-semibold text-primary mb-3">L<PERSON>ch sử thanh toán</h3>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
      ></div>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="!listPayment || listPayment.length === 0"
      class="text-center py-8"
    >
      <div class="text-gray-500">Ch<PERSON>a c<PERSON> lịch sử thanh toán</div>
    </div>

    <!-- Payment history table -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full table-auto bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">
              Th<PERSON><PERSON> gian
            </th>
            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">
              PTTT
            </th>
            <th class="px-4 py-2 text-center text-xs font-medium text-gray-500">
              Trạng thái
            </th>
            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">
              Mã giao dịch
            </th>
            <th
              class="px-4 py-2 text-right text-xs font-medium text-gray-500 w-32"
            >
              Số tiền
            </th>
            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">
              Ghi chú
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-100">
          <tr
            v-for="payment in listPayment"
            :key="payment.paymentId"
            class="hover:bg-gray-50 transition-colors duration-200"
          >
            <!-- Thời gian thanh toán -->
            <td class="px-4 py-3 text-sm text-gray-700">
              <div class="font-medium">
                {{
                  formatTimestampV7(payment.transactionDate || payment.payDate)
                }}
              </div>
            </td>

            <!-- Phương thức -->
            <td class="px-4 py-3 text-sm text-gray-700">
              <span>{{ payment.methodDescription || "N/A" }}</span>
            </td>

            <!-- Trạng thái -->
            <td class="px-4 py-3 text-center">
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                :class="getStatusBadgeClass(payment.statusCode)"
              >
                {{ getStatusText(payment.statusCode) }}
              </span>
            </td>

            <!-- Mã giao dịch -->
            <td class="px-4 py-3 text-sm text-gray-700">
              <div
                class="font-mono text-xs bg-gray-100 px-2 py-1 rounded max-w-xs truncate"
              >
                {{ payment.paymentId || "N/A" }}
              </div>
            </td>

            <!-- Số tiền -->
            <td class="px-4 py-3 text-sm text-right w-32">
              <div class="font-semibold text-gray-900">
                {{
                  formatCurrency(
                    payment.totalAmount || payment.appliedAmount || 0
                  )
                }}
              </div>
            </td>

            <!-- Ghi chú -->
            <td class="px-4 py-3 text-sm text-gray-700">
              <div class="max-w-xs whitespace-pre-wrap break-words">
                {{ payment.orderInfo || payment.paymentNote || "--" }}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);

// Import utils
import { formatCurrency } from "~/utils/formatCurrency";

// Composables
const { paymentsByOrders } = usePayment();

// Reactive state
const listPayment = ref([]);
const loading = ref(false);

// Helper functions
const getStatusText = (statusCode) => {
  switch (statusCode) {
    case "0":
      return "Thành công";
    case "1":
      return "Đang chờ";
    case "-1":
      return "Thất bại";
    case "2":
      return "Đã hủy";
    default:
      return "Không xác định";
  }
};

const getStatusBadgeClass = (statusCode) => {
  switch (statusCode) {
    case "0":
      return "bg-green-100 text-green-800";
    case "1":
      return "bg-yellow-100 text-yellow-800";
    case "-1":
      return "bg-red-100 text-red-800";
    case "2":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Computed properties
const totalPaidAmount = computed(() => {
  return listPayment.value
    .filter((payment) => payment.statusCode === "0") // Only successful payments
    .reduce((total, payment) => {
      return total + (payment.totalAmount || payment.appliedAmount || 0);
    }, 0);
});

// Methods
const getPayment = async () => {
  if (!props.order?.id) return;

  try {
    loading.value = true;
    const response = await paymentsByOrders([props.order.id]);

    // Sort by transaction date (newest first)
    listPayment.value = [...(response || [])].sort(
      (a, b) =>
        new Date(b.transactionDate || b.payDate).getTime() -
        new Date(a.transactionDate || a.payDate).getTime()
    );
  } catch (error) {
    console.error("Error loading payment history:", error);
    listPayment.value = [];
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  await getPayment();
});
</script>
