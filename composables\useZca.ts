export default function useZca() {
  const $sdk = useNuxtApp().$sdk;
  const getLoginQr = async (sessionId: string, appId: string) => {
    try {
      const response = await $sdk.zca.loginQR(sessionId, appId);
      useNuxtApp().$toast.success("Đăng nhập Zalo thành công");
      return response;
    } catch (error) {
      useNuxtApp().$toast.warning("<PERSON>ui lòng quét mã lại để đăng nhập");
      throw error;
    }
  };
  const getUrl = () => {
    return $sdk.zca;
  };
  return { getLoginQr, getUrl };
}
