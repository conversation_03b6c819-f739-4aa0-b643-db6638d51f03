/**
 * Tab Synchronization Plugin
 * 
 * This plugin initializes cross-tab communication for synchronizing
 * authentication state and other critical events across browser tabs.
 * 
 * Features:
 * - Early initialization of tab sync system
 * - Global event handling for tab synchronization
 * - Automatic cleanup on page unload
 */

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (process.client) {
    console.log('[TabSync Plugin] Initializing tab synchronization...');
    
    // Initialize tab sync system early
    const { initializeTabSync, subscribe, cleanup } = useTabSync();
    
    // Initialize the tab sync system
    initializeTabSync();
    
    // Set up global event handlers
    const setupGlobalHandlers = () => {
      // Handle page unload - cleanup resources
      window.addEventListener('beforeunload', () => {
        console.log('[TabSync Plugin] Page unloading, cleaning up...');
        cleanup();
      });
      
      // Handle page visibility changes
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          // Tab became visible - could reinitialize if needed
        } else {
        }
      });
      
      // Handle focus events
      window.addEventListener('focus', () => {
        console.log('[TabSync Plugin] Window focused');
      });
      
      window.addEventListener('blur', () => {
        console.log('[TabSync Plugin] Window blurred');
      });
    };
    
    // Set up global handlers
    setupGlobalHandlers();
    
    // Optional: Set up debug logging for all tab sync events
    if (process.dev) {
      const eventTypes = ['LOGOUT', 'LOGIN', 'TOKEN_REFRESH', 'CONTEXT_CHANGE'];
      
      eventTypes.forEach(eventType => {
        subscribe(eventType as any, (event) => {
          console.log(`[TabSync Plugin] Received ${eventType} event:`, event);
        });
      });
    }
    
    console.log('[TabSync Plugin] Tab synchronization initialized successfully');
  }
});
