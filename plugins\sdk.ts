import { defineNuxtPlugin } from "#app";
import { SDK } from "../../storefront-js-client/src/lib/SDK";

export default defineNuxtPlugin((nuxtApp) => {
  const {
    public: { ENV_NAME },
  } = nuxtApp.$config;

  // Use tab-isolated context instead of cookies
  let orgIdValue = "N/A";
  let storeIdValue = "N/A";

  // On client side, get values from tab context
  if (process.client) {
    const { orgId, storeId, initializeTabContext } = useTabContext();

    // Initialize tab context first
    initializeTabContext();

    orgIdValue = orgId.value;
    storeIdValue = storeId.value;
  } else {
    // On server side, fallback to cookies for SSR compatibility
    const orgIdCookie = useCookie("orgId") as Ref<string>;
    const storeIdCookie = useCookie("storeId") as Ref<string>;

    orgIdValue = orgIdCookie.value || "N/A";
    storeIdValue = storeIdCookie.value || "N/A";
  }

  const envName = ENV_NAME === "dev" || ENV_NAME === "live" ? ENV_NAME : "dev";
  const sdk = new SDK(orgIdValue, storeIdValue, envName);

  return {
    provide: {
      sdk,
    },
  };
});
