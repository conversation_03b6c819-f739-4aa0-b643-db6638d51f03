<template>
  <!-- ✅ Optimized ProductCard Component with Tailwind CSS -->
  <article
    :class="cardClasses"
    data-testid="product-card-vertical"
    @click="addProduct(product)"
    @keydown.enter="addProduct(product)"
    @keydown.space.prevent="addProduct(product)"
    role="button"
    :aria-label="`Thêm sản phẩm ${product.title} vào giỏ hàng`"
    tabindex="0"
  >
    <!-- ✅ Enhanced Image Container -->
    <div class="relative overflow-hidden group">
      <!-- ✅ Product Image with Loading States -->
      <NuxtImg
        data-testid="image-slot"
        :alt="`Hình ảnh sản phẩm ${product.title}`"
        :src="handleGetImageProductUrl()"
        :class="imageClasses"
        loading="lazy"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <!-- ✅ Image Loading Placeholder -->
      <div
        v-if="imageLoading"
        class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
      >
        <svg
          class="w-8 h-8 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <!-- ✅ Image Error Fallback -->
      <div
        v-if="imageError"
        class="absolute inset-0 bg-gray-100 flex items-center justify-center"
      >
        <div class="text-center">
          <svg
            class="w-8 h-8 text-gray-400 mx-auto mb-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-xs text-gray-500">Không có ảnh</span>
        </div>
      </div>

      <!-- ✅ Hover Overlay Effect -->
      <div
        class="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-all duration-300"
      ></div>

      <!-- ✅ Product Type Badge -->
      <div v-if="product.subType === 'VARIABLE'" class="absolute top-2 right-2">
        <span
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
            />
          </svg>
          Biến thể
        </span>
      </div>
    </div>

    <!-- ✅ Enhanced Product Information -->
    <div :class="contentClasses">
      <!-- ✅ Product Title with Fixed Height -->
      <div class="flex-shrink-0">
        <h3 :class="titleClasses" data-testid="link">
          {{ product.title }}
        </h3>
      </div>

      <!-- ✅ Enhanced Price Section - Always at Bottom -->
      <div class="flex-shrink-0">
        <div class="flex items-center justify-between mb-2">
          <span :class="priceClasses" data-testid="product-card-vertical-price">
            {{ displayPrice }}
          </span>

          <!-- ✅ Add to Cart Icon -->
          <div
            class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <div
              class="w-6 h-6 bg-primary rounded-full flex items-center justify-center"
            >
              <svg
                class="w-3 h-3 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- ✅ Product ID/SKU Info -->
        <div class="text-xs text-gray-500 truncate">
          ID: {{ product.id }}
          <span v-if="product.sku" class="ml-2">SKU: {{ product.sku }}</span>
        </div>
      </div>
    </div>

    <!-- ✅ Loading State Indicator -->
    <Transition
      enter-active-class="transition-opacity duration-200"
      leave-active-class="transition-opacity duration-200"
      enter-from-class="opacity-0"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isLoading"
        class="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-md"
      >
        <div class="flex flex-col items-center gap-2">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
          <span class="text-xs text-gray-600">Đang thêm...</span>
        </div>
      </div>
    </Transition>
  </article>

  <!-- ✅ Enhanced Modal with Teleport -->
  <Teleport to="body" v-if="isModalOpen">
    <Transition name="modal">
      <ModalProductDetail
        :isOpen="isModalOpen"
        :productId="product.id"
        @close="closeModal"
      />
    </Transition>
  </Teleport>
</template>

<script setup>
// ✅ Optimized ProductCard Component Script
// Lazy load heavy components
const ModalProductDetail = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalProductDetail.vue"),
  delay: 200,
  timeout: 3000,
  errorComponent: () =>
    h("div", { class: "text-red-500 p-4" }, "Failed to load modal"),
});

// Props with TypeScript validation
const props = defineProps({
  product: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

// Composables
const { addProductToOrder } = useOrderStore();
const { getImageProducrUrl } = usePortal();

// Reactive state
const isModalOpen = ref(false);
const isLoading = ref(false);
const imageLoading = ref(true);
const imageError = ref(false);

// Computed properties for better performance
const displayPrice = computed(() => {
  return props.product.price !== null && props.product.price !== undefined
    ? formatCurrency(props.product.price)
    : "Chưa có giá";
});

// Computed classes for better maintainability
const cardClasses = computed(() => [
  "relative border cursor-pointer border-gray-200 rounded-lg overflow-hidden",
  "flex flex-col w-[148px] md:w-[192px] h-[240px] md:h-[280px]", // ✅ Fixed height
  "hover:shadow-lg hover:border-gray-300 hover:-translate-y-1",
  "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50",
  "transition-all duration-300 ease-out",
  "transform hover:scale-[1.02] active:scale-[0.98]",
  "bg-white group",
]);

const imageClasses = computed(() => [
  "object-contain w-full h-[148px] md:h-[156px]",
  "transition-all duration-300",
  "group-hover:scale-105",
  { "opacity-0": imageLoading.value },
]);

const contentClasses = computed(() => [
  "p-3 border-t border-gray-200 flex-1 flex flex-col justify-between",
  "bg-white group-hover:bg-gray-50",
  "transition-colors duration-200",
]);

const titleClasses = computed(() => [
  "text-sm text-gray-900 font-medium leading-5",
  "line-clamp-2 hover:text-primary transition-colors duration-200",
  "group-hover:text-primary-600",
  "h-10 overflow-hidden break-words", // ✅ Fixed height with word break
]);

const priceClasses = computed(() => [
  "font-bold text-sm",
  props.product.price !== null && props.product.price !== undefined
    ? "text-primary"
    : "text-gray-500",
]);

// Methods
const handleGetImageProductUrl = () => {
  try {
    return getImageProducrUrl(props.product.id, "PRODUCT");
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "/placeholder-image.jpg"; // Fallback image
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};

const addProduct = async () => {
  try {
    if (props.product.subType !== "VARIABLE") {
      isLoading.value = true;
      await addProductToOrder(props.product);
      useNuxtApp().$toast.success(
        `Đã thêm ${props.product.title} vào giỏ hàng`
      );
    } else {
      // Variable product - open modal for variant selection
      isModalOpen.value = true;
    }
  } catch (error) {
    console.error("Error adding product:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi thêm sản phẩm");
  } finally {
    isLoading.value = false;
  }
};

const closeModal = () => {
  isModalOpen.value = false;
};

// Lifecycle
onMounted(() => {
  // Preload image
  const img = new Image();
  img.onload = handleImageLoad;
  img.onerror = handleImageError;
  img.src = handleGetImageProductUrl();
});

// Cleanup body class on unmount
onUnmounted(() => {
  document.body.classList.remove("loading");
});

// Watch loading state for body class
watchEffect(() => {
  if (isLoading.value) {
    document.body.classList.add("loading");
  } else {
    document.body.classList.remove("loading");
  }
});
</script>
<style scoped>
/* ✅ Enhanced animations and transitions */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  hyphens: auto;
}

/* ✅ Modal transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* ✅ Enhanced hover effects */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* ✅ Focus styles for accessibility */
[role="button"]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 8px;
}

/* ✅ Smooth image loading */
img {
  transition: opacity 0.3s ease;
}

/* ✅ Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ✅ Pulse animation for loading placeholder */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ✅ Enhanced card hover effects */
.group:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ✅ Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* ✅ CSS containment for performance */
.bg-white {
  contain: layout style;
}
</style>

<style>
/* ✅ Global loading state */
body.loading {
  pointer-events: none;
  overflow: hidden;
}
</style>
