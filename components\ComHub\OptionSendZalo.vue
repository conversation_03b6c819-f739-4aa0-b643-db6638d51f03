<template>
  <!-- hàng nút action -->
  <td class="px-2 py-1 cursor-pointer relative">
    <span @click="ordersStore.handleToogleTootip(diary?.id)">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"
        />
      </svg>
    </span>
    <div
      v-if="ordersStore.tooltip === diary?.id"
      class="absolute bg-white text-sm border border-primary rounded z-20 -top-[35px] -left-[132px]"
    >
      <!-- N<PERSON>t thanh toán -->
      <div
        @click="handlePayment"
        :class="diary?.status === 'CANCELLED' ? ' disabled ' : ''"
        class="flex items-center gap-1 border-b border-primary text-primary hover:bg-primary hover:text-white px-2 py-1 hover:rounded-t"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
            />
          </svg>
        </span>
        <span> Thanh toán </span>
      </div>
      <!-- Nút in hóa đơn -->
      <div
        @click="handlePrintOrder"
        :class="
          diary?.status === 'DRAFT' || diary?.status === 'CANCELLED'
            ? ' disabled '
            : ''
        "
        class="flex hover:bg-primary hover:text-white items-center gap-1 text-primary px-2 py-1 border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
            />
          </svg>
          <span>{{ handleGetPrint(diary?.order?.customAttributes) }}</span>
        </div>
        <span>In</span>
      </div>
      <!-- Nút gửi tin nhắn -->
      <!-- <div
        @click="handleOpenTopicPopUp"
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 rounded-t border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
            />
          </svg>
        </div>
        <span>Gửi tin nhắn</span>
      </div> -->
      <!-- Hủy đơn -->
      <div
        @click="toogleCancelOrder"
        :class="
          diary?.status === 'DRAFT' ||
          diary?.status === 'CANCELLED' ||
          diary?.financialStatusDescription === 'Đã thanh toán'
            ? ' disabled '
            : ''
        "
        class="flex hover:bg-primary hover:text-white items-center gap-1 text-primary px-2 py-1 border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
            />
          </svg>
        </div>
        <span>Hủy đơn</span>
      </div>
      <div
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 border-b border-primary"
        :class="
          diary?.status === 'DRAFT' || diary?.status === 'CANCELLED'
            ? ' disabled '
            : ''
        "
        @click="toogleCompleteOrder"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
            />
          </svg>
        </div>
        <span>Hoàn thành đơn</span>
      </div>
      <div
        v-if="settingOrg?.isExportInvoice"
        @click="toogleExportInvoice"
        :class="diary?.remainTotal === 0 ? '  ' : 'disabled'"
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
            />
          </svg>
        </div>
        <span>Xuất hóa đơn</span>
      </div>
      <!-- Nút gửi zns -->
      <div
        @click="handleOpenZnsPopUp('SEND_ORDER_INFO')"
        :class="
          diary?.status === 'DRAFT' || diary?.status === 'CANCELLED'
            ? ' disabled '
            : ''
        "
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
            />
          </svg>
        </div>
        <span>Gửi đơn hàng</span>
      </div>

      <div
        @click="handleOpenZnsPopUp('SEND_ORDER_RATE')"
        :class="
          diary?.status === 'DRAFT' || diary?.status === 'CANCELLED'
            ? ' disabled '
            : ''
        "
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 border-b border-primary"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
            />
          </svg>
        </div>
        <span>Gửi Đánh giá</span>
      </div>
      <div
        :class="[diary?.status !== 'COMPLETED' ? 'disabled' : '']"
        class="flex items-center gap-1 text-primary hover:bg-primary hover:text-white px-2 py-1 rounded-b"
        @click="handleNavigate"
      >
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M8.25 9.75h4.875a2.625 2.625 0 0 1 0 5.25H12M8.25 9.75 10.5 7.5M8.25 9.75 10.5 12m9-7.243V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185Z"
            />
          </svg>
        </div>
        <span>Đổi trả hàng</span>
      </div>
    </div>
  </td>
  <SendVoucher
    v-if="isOpenTopicPopup"
    @cancel="handleOpenTopicPopUp"
    @confirm="handleSendMessage"
  ></SendVoucher>
  <ZaloZns
    v-if="isOpenZnsPopup"
    @cancel="handleOpenZnsPopUp"
    @confirm="handleSendZns"
    :orderDetail="diary"
    :typeSendZns="typeSendZns"
  ></ZaloZns>
  <ConfirmDialog
    v-if="isAlert"
    :title="'Thông báo'"
    :message="'Đơn đang ở trạng thái nháp bạn có muốn thanh toán'"
    @confirm="confirm"
    @cancel="cancel"
  />
  <CancelOrderPopup
    v-if="isCancelOrderPopup"
    :order="diary"
    :title="'Hủy đơn hàng'"
    :text="`Bạn đang hủy đơn hàng ${diary?.id} vui lòng chọn lý do`"
    :reasonText="'Lý do hủy đơn'"
    :dataReason="dataReason"
    @cancel="toogleCancelOrder"
    @confirm="handleConfirmCancelOrder"
  >
  </CancelOrderPopup>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
  <FFMOrder
    v-if="isOpenCompleteOrder"
    :order="diary"
    @confirm="handleCompleteOrder"
    @cancel="toogleCompleteOrder"
  />
  <ExportInvoicePopup
    v-if="isOpenExportInvoice"
    :order="diary"
    @confirm="toogleExportInvoice"
    @cancel="toogleExportInvoice"
  ></ExportInvoicePopup>
  <WaringWareHouse
    v-if="isAlertWareHouse"
    @cancel="isAlertWareHouse = false"
  ></WaringWareHouse>
  <StorePopup
    v-if="isAlertStore"
    @confirm="handleSetStore"
    @cancel="isAlertStore = false"
  ></StorePopup>
</template>

<script setup lang="ts">
const OptionSendZalo = defineAsyncComponent(
  () => import("~/components/dialog/CancelOrderPopup.vue")
);
const LoadingSpinner = defineAsyncComponent(
  () => import("~/components/common/LoadingSpinner.vue")
);
const CancelOrderPopup = defineAsyncComponent(
  () => import("~/components/dialog/CancelOrderPopup.vue")
);
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);
const ZaloZns = defineAsyncComponent(
  () => import("~/components/ComHub/ZaloZns.vue")
);
const SendVoucher = defineAsyncComponent(
  () => import("~/components/dialog/SendVoucher.vue")
);
const WaringWareHouse = defineAsyncComponent(
  () => import("~/components/dialog/WaringWareHouse.vue")
);
const props = defineProps(["diary", "isNotDraft", "isAgency", "data"]);
const { searchEmployes, cancelOrder } = useOrder();
const route = useRoute();
const ordersStore = useOrdersStore();
const isAlertWareHouse = ref(false);
onMounted(async () => {
  ordersStore.tooltip = null;
});
const emits = defineEmits(["handleLoading"]);
const diaryStore = useDiariesStore();

const isAlert = ref(false);
const isAlertStore = ref(false);
const { setStore } = usePermission();

const handlePayment = async () => {
  if (storeId.value !== subStoreId.value) {
    isAlertStore.value = true;
    return;
  }
  const res = await handleCheckWarehouse();
  if (!res) {
    const dataSettingOrg = props.data as any;
    const arrRoles = dataSettingOrg?.find(
      (org: any) => org?.storeId === orgId.value
    );
    if (arrRoles?.rolesExportInvoice?.length) {
      const arrRolesDefault = ["SALE_ADMIN", "SALE_MANAGER"];
      const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
        arrRolesDefault?.includes(role)
      );
      if (!isRoleSaleAdmin?.length) {
        isAlertWareHouse.value = true;
        return;
      }
    }
  }
  // /payment?orderId=${dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}
  if (props.diary?.status === "DRAFT" || props.diary?.status === "OPEN") {
    console.log("đang là đơn nháp");
    isAlert.value = true;
    //
    return;
  }
  if (props.diary?.remainTotal > 0) {
    navigateTo(
      `/payment?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  } else {
    useNuxtApp().$toast.warning(
      "Không thể thanh toán,số tiền cần thanh toán là 0"
    );
  }
};
const router = useRouter();
const handleSetStore = async () => {
  await setStore(subStoreId.value);
  router.push({
    query: {
      ...router.currentRoute.value.query,
      storeId: subStoreId.value,
    },
  });
  nextTick();
  useNuxtApp().$toast.success(
    "Đã chuyển của hàng thành công, vui lòng thực hiện lại thao tác"
  );
};
//
const cancel = () => {
  isAlert.value = false;
};
const { updateStatusApproved } = useOrder();

const confirm = async () => {
  if (props.diary?.remainTotal > 0) {
    emits("handleLoading", true); // Emit trạng thái loading

    await updateStatusApproved(props.diary?.id);
    navigateTo(
      `/payment?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    emits("handleLoading", false); // Emit trạng thái loading
  } else {
    useNuxtApp().$toast.warning(
      "Không thể thanh toán,số tiền cần thanh toán là 0"
    );
  }
};
const { printOrderHTML } = useOrder();
import printJS from "print-js";
import type { Auth } from "~/types/Auth";
const isLoading = ref(false);

// Hàm validation chung cho storeId và subStoreId
const validateStoreAccess = () => {
  if (storeId.value !== subStoreId.value) {
    isAlertStore.value = true;
    return false;
  }
  return true;
};

const handlePrintOrder = async () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }

  const url = useRequestURL();

  const baseUrl = `${url.origin}/thanh-toan?orderId=${
    props.diary?.id
  }&orgId=${url.searchParams.get("orgId")}&storeId=${url.searchParams.get(
    "storeId"
  )}`;
  try {
    const response = await printOrderHTML(
      props.diary?.id,
      "Chưa thanh toán",
      baseUrl
    );

    const data = response.data;

    printJS({
      printable: data,
      type: "raw-html",
      scanStyles: false,
      style: `
        @page { margin: 0; } /* Xóa margin mặc định của trang in */
        body { margin: 0; } /* Đảm bảo body không có margin thừa */
      `,
    });

    if (props.isNotDraft) {
      ordersStore.updateQuantityPrintOrder(props?.diary);
    } else {
      diaryStore.updateQuantityPrintOrder(props?.diary);
    }
  } catch (error) {
    console.error("Error printing the order:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleGetPrint = (Items: any) => {
  const isPrint = Items.find((item: any) => item.key === "printTimes");
  if (isPrint) {
    return `(${isPrint?.value})`;
  } else {
    return "(0)";
  }
};
const isOpenTopicPopup = ref(false);
const handleOpenTopicPopUp = () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }

  if (props.diary?.order?.ownerPartyId) {
    ordersStore.tooltip = null;
    isOpenTopicPopup.value = !isOpenTopicPopup.value;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const { createTopic } = useCustomer();

const handleCreateTopic = async (message: string) => {
  if (props.diary?.order?.ownerPartyId) {
    await createTopic(
      "674fcd11b1538b122be026d0",
      props.diary?.order?.ownerPartyId,
      message
    );
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
  isOpenTopicPopup.value = false;
};
// send message
const { sendMessage, shareOrder } = useComhub();
const handleSendMessage = async (message: string) => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  const zaloId = appId.find((app: any) => app.name === "ZNS");
  if (!zaloId) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  if (!props.diary?.order?.ownerPartyId) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    isOpenTopicPopup.value = false;
    return;
  }

  try {
    await sendMessage(zaloId?.id, message, "TEXT", "SYSTEM", [
      props.diary?.order?.ownerPartyId,
    ]);
  } catch (error) {
    console.error("Error sending message:", error);
  }

  isOpenTopicPopup.value = false;
};
// gửi zns
const isOpenZnsPopup = ref(false);
const typeSendZns = ref();
const handleOpenZnsPopUp = (type: string) => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }

  if (props.diary?.order?.ownerPartyId) {
    ordersStore.tooltip = null;
    typeSendZns.value = type;
    isOpenZnsPopup.value = !isOpenZnsPopup.value;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const handleSendZns = async (data: any) => {
  isLoading.value = true;
  try {
    await shareOrder(
      data?.templateData,
      data?.app?.apps[0]?.id,
      data?.templateType
    );
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    throw error;
  }
  isOpenZnsPopup.value = false;
};
//
const isCancelOrderPopup = ref();
const toogleCancelOrder = () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }

  if (
    props.diary?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isCancelOrderPopup.value = !isCancelOrderPopup.value;
};
const handleConfirmCancelOrder = async (reason: string) => {
  try {
    const auth = useCookie("auth").value as unknown as Auth;
    const data = {
      reason: "CUSTOMER",
      updatedBy: auth?.user?.id,
      note: reason,
      orderType: "SALES",
    };
    await cancelOrder(props.diary?.id, data);
    useNuxtApp().$toast.success("Hủy đơn hàng thành công");
    if (props.isNotDraft) {
      ordersStore.upDateCancelOrderStatus(props?.diary, reason);
    } else {
      diaryStore.upDateCancelOrderStatus(props?.diary, reason);
    }
  } catch (error) {
    throw error;
  } finally {
    ordersStore.tooltip = null;
    toogleCancelOrder();
  }
};
const handleNavigate = async () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }

  navigateTo(
    `/order/return?orderReturnId=${props.diary?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};
const isOpenCompleteOrder = ref(false);
const toogleCompleteOrder = () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }
  isOpenCompleteOrder.value = !isOpenCompleteOrder.value;
};
const { completeOrder } = usePortal();
const auth = useCookie("auth") as any;
const handleCompleteOrder = async () => {
  try {
    const response = await completeOrder(props.diary?.id, auth.value?.user?.id);
    if (response?.status === 1.0) {
      ordersStore.updateFFMStatus(props.diary);
    }
  } catch (error) {
    throw error;
  } finally {
    toogleCompleteOrder();
  }
};
const dataReason = [
  { name: "Chọn lý do hủy" },
  {
    name: "Khách hàng yêu cầu",
  },
  {
    name: "Thông tin chưa hợp lệ",
  },
  {
    name: "Không đủ hàng trong kho",
  },
  {
    name: "Không thanh toán đơn hàng",
  },
  {
    name: "Khác",
  },
];
const isOpenExportInvoice = ref(false);
const { storeId, orgId, subStoreId } = useTabContext();
const toogleExportInvoice = () => {
  // Validation storeId và subStoreId
  if (!validateStoreAccess()) {
    return;
  }
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
const { getInventoryV2 } = useWarehouse();
const settingOrg = computed(() => {
  const dataSettingOrg = props.data as any;
  return dataSettingOrg?.find((org: any) => org?.storeId === orgId.value);
});
const handleCheckWarehouse = async () => {
  const dataSettingOrg = props.data as any;
  const result = dataSettingOrg?.find(
    (org: any) => org?.storeId === orgId.value
  );
  if (result?.isExportInvoiceForProduct) {
    const data = <any>[];
    props.diary?.activeOrderItemProfiles?.map(async (item: any) => {
      const sku = item.orderLineItem.variant.sku;
      const test = {
        productId: item?.orderLineItem.variant?.product?.id,
        variantId:
          item?.orderLineItem?.variant?.id ===
          item.orderLineItem.variant?.product?.id
            ? ""
            : item.orderLineItem.variant?.id,
        sku: item.orderLineItem.variant?.sku,
      };
      data.push(test);
    });
    const res = await getInventoryV2(
      props.diary?.order?.customAttribute?.facilityId,
      data
    );

    if (res?.length) {
      for (const item of res) {
        if (item?.orderAble < 5) {
          return false;
        }
      }
    }
    return true;
  } else {
    return true;
  }
};
</script>
<style scoped>
.disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>
