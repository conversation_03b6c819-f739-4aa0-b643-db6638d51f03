# Simple Dockerfile for Nuxt.js TeleSale Application
FROM node:20-alpine

# Install wget for health check
RUN apk add --no-cache wget

WORKDIR /app

# Copy the pre-built application from CI
COPY .output/ ./.output/

# Build arguments
ARG ENV=production
ARG NODE_ENV=production

# Set environment variables
ENV NODE_ENV=$NODE_ENV
ENV NITRO_PRESET=node-server

# Expose port
EXPOSE 3000

# Set environment variables
ENV PORT=3000
ENV HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Start the application
CMD ["node", ".output/server/index.mjs"]
