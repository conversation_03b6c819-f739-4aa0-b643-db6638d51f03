<template>
  <div class="mx-2 mt-2">
    <div class="grid grid-cols-12 gap-2">
      <!-- Sidebar Navigation -->
      <div class="col-span-12 lg:col-span-3">
        <TabSetting v-model="activeTab" @tab-change="handleTabChange" />
      </div>

      <!-- Main Content Area -->
      <div class="col-span-12 lg:col-span-9 h-screen-50 overflow-y-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <!-- Content Header -->
          <div class="border-b border-gray-200 px-6 py-3">
            <div class="flex items-center gap-3">
              <div class="w-6 h-6 text-primary" v-html="currentTabData?.icon" />
              <h1 class="text-xl font-semibold text-gray-900">
                {{ currentTabData?.label }}
              </h1>
            </div>
          </div>

          <!-- Dynamic Content -->
          <div class="p-6">
            <!-- Dynamic Component Content -->
            <Transition name="fade" mode="out-in">
              <Suspense>
                <component
                  :is="currentTabComponent"
                  :key="activeTab"
                  class="min-h-[400px]"
                />
                <template #fallback>
                  <div class="min-h-[400px] flex items-center justify-center">
                    <div class="text-center">
                      <div
                        class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"
                      ></div>
                      <p class="text-gray-500">Đang tải nội dung...</p>
                    </div>
                  </div>
                </template>
              </Suspense>
            </Transition>

            <!-- Error Fallback -->
            <div
              v-if="!currentTabComponent"
              class="min-h-[400px] flex items-center justify-center"
            >
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <svg
                    class="w-8 h-8 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  Không thể tải nội dung
                </h3>
                <p class="text-gray-500">
                  Có lỗi xảy ra khi tải nội dung cho tab: {{ activeTab }}
                </p>
                <button
                  @click="$router.go(0)"
                  class="mt-4 bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90"
                >
                  Tải lại trang
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission"],
  name: "Cấu hình",
});
useHead({
  title: "Cấu hình",
  meta: [{ name: "description", content: "Cấu hình" }],
});
const route = useRoute();
const router = useRouter();

const tabsData = [
  {
    value: "general",
    label: "Cài đặt chung",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>`,
  },
  {
    value: "account",
    label: "Tài khoản",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
    </svg>`,
  },
  {
    value: "security",
    label: "Bảo mật",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
    </svg>`,
  },
  {
    value: "notifications",
    label: "Thông báo",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
    </svg>`,
  },
  {
    value: "billing",
    label: "Gói trả phí",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
    </svg>`,
  },
  {
    value: "integrations",
    label: "Tích hợp",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
    </svg>`,
  },
  {
    value: "paymentMethod",
    label: "Cấu hình PTTT",
    icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
    </svg>`,
  },
];

const activeTab = ref((route.query.tab as string) || "general");

const currentTabData = computed(() => {
  return tabsData.find((tab) => tab.value === activeTab.value) || tabsData[0];
});

const SettingsGeneral = defineAsyncComponent(
  () => import("~/components/Settings/SettingsGeneral.vue")
);
const SettingsAccount = defineAsyncComponent(
  () => import("~/components/Settings/SettingsAccount.vue")
);
const SettingsSecurity = defineAsyncComponent(
  () => import("~/components/Settings/SettingsSecurity.vue")
);
const SettingsNotifications = defineAsyncComponent(
  () => import("~/components/Settings/SettingsNotifications.vue")
);
const SettingsBilling = defineAsyncComponent(
  () => import("~/components/Settings/SettingsBilling.vue")
);
const SettingsIntegrations = defineAsyncComponent(
  () => import("~/components/Settings/SettingsIntegrations.vue")
);
const SettingsPaymentMethod = defineAsyncComponent(
  () => import("~/components/Settings/SettingsPaymentMethod.vue")
);
const currentTabComponent = computed(() => {
  const componentMap: Record<string, any> = {
    general: SettingsGeneral,
    account: SettingsAccount,
    security: SettingsSecurity,
    notifications: SettingsNotifications,
    billing: SettingsBilling,
    integrations: SettingsIntegrations,
    paymentMethod: SettingsPaymentMethod,
  };

  return componentMap[activeTab.value] || SettingsGeneral;
});

const handleTabChange = (tabValue: string) => {
  activeTab.value = tabValue;
};

watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === "string") {
      activeTab.value = newTab;
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (!route.query.tab) {
    router.replace({
      path: route.path,
      query: { ...route.query, tab: "general" },
    });
  }
});
</script>

<style scoped>
/* Fade transition for content switching */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
