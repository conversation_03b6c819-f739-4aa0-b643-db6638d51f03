<template>
  <div class="p-2 bg-white rounded">
    <h3 class="text-lg font-semibold text-primary"><PERSON><PERSON> chú</h3>
    <textarea
      rows="3"
      id="note"
      class="py-1 px-2 w-full md:text-sm text-base rounded outline-none border bg-secondary"
      placeholder="Ghi chú đơn hàng"
      :disabled="true"
      v-model="note"
    ></textarea>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);
const note = computed(() => {
  return props.order?.order?.note || "";
});
</script>
