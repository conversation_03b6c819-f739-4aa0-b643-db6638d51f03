<template>
  <div class="p-2 rounded bg-white">
    <h3 class="text-lg font-semibold text-primary mb-3">Đơn hàng liên quan</h3>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-4">
      <div
        class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
      ></div>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="!orderRelate || orderRelate.length === 0"
      class="text-center py-4"
    >
      <div class="text-gray-500 text-sm">Không có đơn hàng liên quan</div>
    </div>

    <!-- Related orders list -->
    <div v-else class="space-y-3">
      <div
        v-for="order in orderRelate"
        :key="order.order_id"
        class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors duration-200"
      >
        <!-- Order header -->
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center gap-2">
            <span class="font-semibold text-gray-900">{{
              order.order_id
            }}</span>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="getOrderTypeBadgeClass(order.type)"
            >
              {{ getOrderTypeText(order.type) }}
            </span>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatTimestampV7(order.created_at) }}
          </div>
        </div>

        <!-- Order details -->
        <div class="space-y-1 text-sm">
          <div v-if="order.customer_name" class="flex justify-between">
            <span class="text-gray-600">Khách hàng:</span>
            <span class="text-gray-900">{{ order.customer_name }}</span>
          </div>

          <div v-if="order.total_amount" class="flex justify-between">
            <span class="text-gray-600">Tổng tiền:</span>
            <span class="font-semibold text-gray-900">
              {{ formatCurrency(order.total_amount) }}
            </span>
          </div>

          <div v-if="order.status" class="flex justify-between">
            <span class="text-gray-600">Trạng thái:</span>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="getStatusBadgeClass(order.status)"
            >
              {{ getStatusText(order.status) }}
            </span>
          </div>

          <div v-if="order.employee_name" class="flex justify-between">
            <span class="text-gray-600">Nhân viên:</span>
            <span class="text-gray-900">{{ order.employee_name }}</span>
          </div>

          <div v-if="order.note" class="mt-2">
            <span class="text-gray-600">Ghi chú:</span>
            <div
              class="text-gray-900 text-xs mt-1 p-2 bg-gray-50 rounded whitespace-pre-wrap"
            >
              {{ order.note }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);

// Import utils
import { formatCurrency } from "~/utils/formatCurrency";

// Composables
const { fetchListOrderRelations } = useOrder();

// Reactive state
const orderRelate = ref();
const loading = ref(false);

// Helper functions
const getOrderTypeText = (type) => {
  switch (type) {
    case "POS_SALE":
      return "Đơn gốc";
    case "POS_RETURN":
      return "Đơn trả";
    case "POS_EXCHANGE":
      return "Đơn đổi";
    default:
      return "Khác";
  }
};

const getOrderTypeBadgeClass = (type) => {
  switch (type) {
    case "POS_SALE":
      return "bg-blue-100 text-blue-800";
    case "POS_RETURN":
      return "bg-red-100 text-red-800";
    case "POS_EXCHANGE":
      return "bg-orange-100 text-orange-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusText = (status) => {
  switch (status) {
    case "COMPLETED":
      return "Hoàn thành";
    case "PENDING":
      return "Đang chờ";
    case "CANCELLED":
      return "Đã hủy";
    case "PROCESSING":
      return "Đang xử lý";
    default:
      return status || "Không xác định";
  }
};

const getStatusBadgeClass = (status) => {
  switch (status) {
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    case "PROCESSING":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Methods
const getOrderRelate = async () => {
  if (!props.order?.id) return;

  try {
    loading.value = true;
    const response = await fetchListOrderRelations([props.order.id]);
    orderRelate.value = response?.data || [];
    console.log("orderRelate", orderRelate.value);
  } catch (error) {
    console.error("Error fetching related orders:", error);
    orderRelate.value = [];
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  await getOrderRelate();
});
</script>
