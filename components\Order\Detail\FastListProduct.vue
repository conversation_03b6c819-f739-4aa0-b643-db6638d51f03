<template>
  <div class="bg-white p-2 rounded">
    <h3 class="text-lg font-semibold text-primary"><PERSON><PERSON> s<PERSON>ch sản phẩm</h3>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
      ></div>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="!productList || productList.length === 0"
      class="text-center py-8"
    >
      <div class="text-gray-500">Không có sản phẩm nào trong đơn hàng</div>
    </div>

    <!-- Products table -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full table-auto bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              Ảnh
            </th>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              T<PERSON><PERSON> sản phẩm
            </th>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              Đơn giá
            </th>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              Số lượng
            </th>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              Giảm giá
            </th>
            <th
              class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
            >
              Thành tiền
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-100">
          <tr
            v-for="item in productList"
            :key="item.id"
            class="hover:bg-gray-50 transition-colors duration-200"
          >
            <!-- Product Image -->
            <td class="px-4 py-3">
              <div class="w-12 h-12 flex-shrink-0">
                <NuxtImg
                  :src="getProductImage(item.orderLineItem)"
                  :alt="getProductName(item.orderLineItem)"
                  class="w-full h-full object-contain rounded-lg border border-gray-200"
                  loading="lazy"
                  preload
                />
              </div>
            </td>

            <!-- Product Name -->
            <td class="px-4 py-3">
              <div class="max-w-xs">
                <div class="text-sm line-clamp-2">
                  {{ getProductName(item.orderLineItem) }}
                </div>
                <div
                  v-if="item.orderLineItem.variant?.sku"
                  class="text-xs text-gray-500 mt-1"
                >
                  SKU: {{ item.orderLineItem.variant.sku }}
                </div>
              </div>
            </td>

            <!-- Unit Price -->
            <td class="px-4 py-3 text-sm text-gray-700">
              {{ formatCurrency(item?.orderLineItem?.variant?.price?.amount) }}
            </td>

            <!-- Quantity -->
            <td class="px-4 py-3 text-sm text-gray-700">
              {{ item.orderLineItem.quantity }}
            </td>

            <!-- Discount -->
            <td class="px-4 py-3 text-sm text-gray-700">
              <span v-if="getDiscountAmount(item.orderLineItem) > 0">
                {{ formatCurrency(getDiscountAmount(item.orderLineItem)) }}
              </span>
              <span v-else class="text-gray-400">0</span>
            </td>

            <!-- Total Amount -->
            <td class="px-4 py-3 text-sm font-semibold text-gray-900">
              {{ formatCurrency(getTotalAmount(item.orderLineItem)) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);

// Import utils
import { formatCurrency } from "~/utils/formatCurrency";

// Loading state
const loading = ref(false);

// Computed properties
const productList = computed(() => {
  return props.order?.activeOrderItemProfiles || [];
});
const { getImageProducrUrl } = usePortal();

// Helper functions
const getProductImage = (orderLineItem) => {
  // Use the same method as ItemTableCard component
  if (orderLineItem?.variant?.id) {
    return getImageProducrUrl(orderLineItem.variant.id, "PRODUCT");
  }
  return "https://placehold.co/80";
};

const getProductName = (orderLineItem) => {
  const variantTitle = orderLineItem?.variant?.title;
  const productTitle = orderLineItem?.variant?.product?.title;
  return variantTitle || productTitle || "Sản phẩm không có tên";
};

const getDiscountAmount = (orderLineItem) => {
  // Lấy giảm giá từ discount object nếu có, nếu không thì trả về 0
  const discountAmount = orderLineItem?.discount?.value?.amount || 0;
  return discountAmount;
};

const getTotalAmount = (orderLineItem) => {
  // Lấy thành tiền từ discountedTotalPrice nếu có, nếu không thì tính từ originalTotalPrice
  const discountedTotal = orderLineItem?.discountedTotalPrice?.amount;
  const originalTotal = orderLineItem?.originalTotalPrice?.amount;
  return discountedTotal || originalTotal || 0;
};
</script>
