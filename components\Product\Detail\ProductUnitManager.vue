<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-4 py-2 sm:py-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-orange-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9"
            />
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Đ<PERSON><PERSON> vị quy đổi</h2>
          <p class="text-sm text-gray-500">Quản lý đơn vị tính của sản phẩm</p>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-2 space-y-2">
      <!-- Current Unit Display -->
      <div v-if="currentUnit" class="bg-blue-50 rounded-lg p-4">
        <div class="flex items-center gap-3">
          <div
            class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-blue-900">Đơn vị hiện tại</p>
            <p class="text-lg font-semibold text-blue-800">
              {{ currentUnit.name }}
            </p>
          </div>
        </div>
      </div>

      <!-- Unit Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Chọn đơn vị tính
          <span class="text-red-500">*</span>
        </label>

        <div class="relative">
          <select
            v-model="originalUnit"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 appearance-none bg-white"
            @change="handleChangeOriginalUnit"
          >
            <option value="" disabled>Chọn đơn vị tính</option>
            <option v-for="unit in dataUnit" :key="unit?.id" :value="unit?.id">
              {{ unit?.name }}
            </option>
          </select>

          <!-- Custom dropdown arrow -->
          <div
            class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- Available Units List -->
      <!-- <div v-if="dataUnit.length > 0">
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Danh sách đơn vị có sẵn
        </label>

        <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
          <div
            v-for="unit in dataUnit"
            :key="unit.id"
            :class="[
              'p-3 border rounded-lg text-center cursor-pointer transition-all duration-200',
              originalUnit === unit.id
                ? 'border-primary bg-primary/5 text-primary'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50',
            ]"
            @click="selectUnit(unit.id)"
          >
            <div class="text-sm font-medium">{{ unit.name }}</div>
          </div>
        </div>
      </div> -->

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-4">
        <div class="inline-flex items-center gap-2 text-gray-500">
          <div
            class="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"
          ></div>
          <span class="text-sm">Đang cập nhật...</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { Auth } from "~/types/Auth";
import type { UnitDTO } from "~/types/Product";

interface Props {
  product: any;
}

const props = defineProps<Props>();
const emit = defineEmits(["update"]);

const { getUnits, updateUnit } = useProduct();
const dataUnit = ref<UnitDTO[]>([]);
const originalUnit = ref<string>("");
const isLoading = ref<boolean>(false);

// Computed property for current unit
const currentUnit = computed(() => {
  if (!originalUnit.value || !dataUnit.value.length) return null;
  return dataUnit.value.find((unit) => unit.id === originalUnit.value);
});

// Watch for product changes
watch(
  () => props.product,
  (newVal) => {
    if (newVal?.unitDTO?.id) {
      originalUnit.value = newVal.unitDTO.id;
    }
  },
  { immediate: true }
);

// Load units
const handleGetUnit = async () => {
  try {
    isLoading.value = true;
    const response = await getUnits();
    dataUnit.value = response;
  } catch (error) {
    console.error("Error loading units:", error);
  } finally {
    isLoading.value = false;
  }
};

// Update unit
const auth = useCookie<Auth | null>("auth");

const handleChangeOriginalUnit = async () => {
  if (!originalUnit.value) return;

  try {
    isLoading.value = true;
    await updateUnit(
      props.product?.id,
      originalUnit.value,
      auth.value?.user?.id as string
    );
    emit("update");
  } catch (error) {
    console.error("Error updating unit:", error);
  } finally {
    isLoading.value = false;
  }
};

// Select unit from grid
const selectUnit = async (unitId: string) => {
  originalUnit.value = unitId;
  await handleChangeOriginalUnit();
};

// Initialize
onMounted(async () => {
  await handleGetUnit();
});
</script>
