<template>
  <!-- ✅ Optimized Modal with Teleport and Accessibility -->
  <Teleport to="body">
    <Transition
      name="modal"
      appear
      @before-enter="onBeforeEnter"
      @after-enter="onAfterEnter"
      @before-leave="onBeforeLeave"
      @after-leave="onAfterLeave"
    >
      <div
        v-show="isVisible"
        ref="modalRef"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
        @click.self="handleBackdropClick"
        role="dialog"
        aria-modal="true"
        :aria-labelledby="titleId"
        :aria-describedby="messageId"
        @keydown="handleKeydown"
      >
        <!-- ✅ Enhanced Modal Content with better responsive design -->
        <div
          class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          style="contain: layout style paint"
          @click.stop
        >
          <!-- ✅ Enhanced Header with icon support -->
          <div class="px-6 pt-6 pb-4">
            <div
              class="flex items-center justify-center mb-4"
              v-if="showIcon && (icon || variant !== 'default')"
            >
              <div
                class="w-12 h-12 rounded-full flex items-center justify-center"
                :class="iconContainerClass"
              >
                <!-- Custom icon if provided -->
                <component
                  v-if="icon"
                  :is="icon"
                  class="w-6 h-6"
                  :class="iconClass"
                />
                <!-- Simple SVG icons for variants -->
                <svg
                  v-else
                  class="w-6 h-6"
                  :class="iconClass"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    :d="iconPath"
                    :fill-rule="iconFillRule"
                    :clip-rule="iconClipRule"
                  />
                </svg>
              </div>
            </div>

            <h2
              :id="titleId"
              class="text-xl font-semibold text-center text-gray-900 leading-tight"
            >
              {{ title || defaultTitle }}
            </h2>
          </div>

          <!-- ✅ Enhanced Message Content -->
          <div class="px-6 pb-6">
            <p
              :id="messageId"
              class="text-gray-600 text-center leading-relaxed"
              v-html="formattedMessage"
            ></p>
          </div>

          <!-- ✅ Enhanced Action Buttons -->
          <div class="px-6 pb-6">
            <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <!-- Cancel Button -->
              <button
                v-if="!isHideButton"
                ref="cancelButtonRef"
                @click="cancel"
                @keydown.enter="cancel"
                class="order-2 sm:order-1 px-4 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 focus:bg-gray-200 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2"
                :disabled="isLoading"
              >
                {{ cancelText }}
              </button>

              <!-- Confirm Button -->
              <button
                ref="confirmButtonRef"
                @click="confirm"
                @keydown.enter="confirm"
                class="order-1 sm:order-2 px-4 py-2.5 text-white rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                :class="confirmButtonClass"
                :disabled="isLoading"
              >
                <!-- Loading spinner -->
                <svg
                  v-if="isLoading"
                  class="animate-spin w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                {{ isLoading ? loadingText : confirmText }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue";

// ✅ Enhanced Props with TypeScript support
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  message: {
    type: String,
    default: "",
  },
  isHideButton: {
    type: Boolean,
    default: false,
  },
  variant: {
    type: String,
    default: "default",
    validator: (value) =>
      ["default", "danger", "warning", "success", "info"].includes(value),
  },
  icon: {
    type: String,
    default: "",
  },
  confirmText: {
    type: String,
    default: "Đồng ý",
  },
  cancelText: {
    type: String,
    default: "Đóng",
  },
  loadingText: {
    type: String,
    default: "Đang xử lý...",
  },
  allowBackdropClose: {
    type: Boolean,
    default: true,
  },
  autoFocus: {
    type: Boolean,
    default: true,
  },
  showIcon: {
    type: Boolean,
    default: false,
  },
});

// ✅ Enhanced Emits
const emit = defineEmits(["confirm", "cancel", "backdrop-click", "closed"]);

// ✅ Reactive State
const isVisible = ref(true);
const isLoading = ref(false);
const isAnimating = ref(false);

// ✅ Template Refs
const modalRef = ref(null);
const confirmButtonRef = ref(null);
const cancelButtonRef = ref(null);

// ✅ Unique IDs for accessibility
const titleId = computed(
  () => `confirm-dialog-title-${Math.random().toString(36).substring(2, 11)}`
);
const messageId = computed(
  () => `confirm-dialog-message-${Math.random().toString(36).substring(2, 11)}`
);

// ✅ Computed Properties
const defaultTitle = computed(() => {
  const titles = {
    danger: "Xác nhận xóa",
    warning: "Cảnh báo",
    success: "Thành công",
    info: "Thông tin",
    default: "Xác nhận",
  };
  return titles[props.variant] || titles.default;
});

const formattedMessage = computed(() => {
  return props.message.replace(/\n/g, "<br>");
});

const iconPath = computed(() => {
  const paths = {
    danger:
      "M9 2a1 1 0 000 2h2a1 1 0 100-2H9z M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012-2h4a1 1 0 110 2H8a1 1 0 01-1-1z",
    warning:
      "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",
    success:
      "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
    info: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",
    default:
      "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",
  };
  return paths[props.variant] || paths.default;
});

const iconFillRule = computed(() => {
  return props.variant === "warning" || props.variant === "success"
    ? "evenodd"
    : undefined;
});

const iconClipRule = computed(() => {
  return props.variant === "warning" || props.variant === "success"
    ? "evenodd"
    : undefined;
});

const iconContainerClass = computed(() => {
  const classes = {
    danger: "bg-red-100",
    warning: "bg-orange-100",
    success: "bg-green-100",
    info: "bg-blue-100",
    default: "bg-gray-100",
  };
  return classes[props.variant] || classes.default;
});

const iconClass = computed(() => {
  const classes = {
    danger: "text-red-600",
    warning: "text-orange-600",
    success: "text-green-600",
    info: "text-blue-600",
    default: "text-gray-600",
  };
  return classes[props.variant] || classes.default;
});

const confirmButtonClass = computed(() => {
  const classes = {
    danger: "bg-red-600 hover:bg-red-700 focus:ring-red-500",
    warning: "bg-orange-600 hover:bg-orange-700 focus:ring-orange-500",
    success: "bg-green-600 hover:bg-green-700 focus:ring-green-500",
    info: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",
    default: "bg-primary hover:bg-primary/90 focus:ring-primary",
  };
  return classes[props.variant] || classes.default;
});

// ✅ Methods
const confirm = () => {
  if (isLoading.value) return;

  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  if (isLoading.value) return;

  emit("cancel");
  isVisible.value = false;
};

const handleBackdropClick = () => {
  if (!props.allowBackdropClose || isLoading.value || isAnimating.value) return;

  emit("backdrop-click");
  cancel();
};

const handleDocumentClick = (event) => {
  // Enhanced click outside detection
  if (!modalRef.value || isAnimating.value) return;

  const modalElement = modalRef.value;
  const clickedElement = event.target;

  // Check if click is outside modal content
  if (!modalElement.contains(clickedElement)) {
    handleBackdropClick();
  }
};

// ✅ Keyboard Navigation
const handleKeydown = (event) => {
  if (event.key === "Escape" && props.allowBackdropClose && !isLoading.value) {
    cancel();
  } else if (event.key === "Tab") {
    // Trap focus within dialog
    const focusableElements = [
      cancelButtonRef.value,
      confirmButtonRef.value,
    ].filter(Boolean);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault();
      lastElement?.focus();
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault();
      firstElement?.focus();
    }
  }
};

// ✅ Transition Hooks
const onBeforeEnter = () => {
  isAnimating.value = true;
  // Prevent body scroll when modal opens
  document.body.style.overflow = "hidden";
  document.body.style.paddingRight = getScrollbarWidth() + "px";
};

const onAfterEnter = () => {
  isAnimating.value = false;
  // Auto focus after animation completes
  if (props.autoFocus) {
    nextTick(() => {
      const targetButton = props.isHideButton
        ? confirmButtonRef.value
        : cancelButtonRef.value;
      targetButton?.focus();
    });
  }
};

const onBeforeLeave = () => {
  isAnimating.value = true;
};

const onAfterLeave = () => {
  isAnimating.value = false;
  // Restore body scroll when modal closes
  document.body.style.overflow = "";
  document.body.style.paddingRight = "";

  // Emit close event for cleanup
  emit("closed");
};

// ✅ Utility Functions
const getScrollbarWidth = () => {
  const scrollDiv = document.createElement("div");
  scrollDiv.style.cssText =
    "width: 100px; height: 100px; overflow: scroll; position: absolute; top: -9999px;";
  document.body.appendChild(scrollDiv);
  const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
  document.body.removeChild(scrollDiv);
  return scrollbarWidth;
};

// ✅ Lifecycle Hooks
onMounted(() => {
  // Add global event listeners
  document.addEventListener("keydown", handleKeydown);

  // Add click outside listener for better UX
  document.addEventListener("click", handleDocumentClick);
});

onUnmounted(() => {
  // Remove all event listeners
  document.removeEventListener("keydown", handleKeydown);
  document.removeEventListener("click", handleDocumentClick);

  // Ensure body scroll is restored
  document.body.style.overflow = "";
  document.body.style.paddingRight = "";
});

// ✅ Expose methods for parent component
defineExpose({
  confirm,
  cancel,
  setLoading: (loading) => {
    isLoading.value = loading;
  },
});
</script>

<style scoped>
/* ✅ Enhanced Modal Transitions */
.modal-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 1, 1);
}

.modal-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.modal-enter-to,
.modal-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* ✅ Hardware Acceleration */
.modal-enter-active,
.modal-leave-active {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ✅ Backdrop Blur Support */
@supports (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
}

/* ✅ Focus Trap Enhancement */
.focus-trap {
  outline: none;
}

/* ✅ Loading Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ✅ Button Hover Effects */
button {
  transform: translateY(0);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* ✅ Mobile Optimizations */
@media (max-width: 640px) {
  .modal-content {
    margin: 1rem;
    max-height: 90vh;
  }

  /* Stack buttons vertically on mobile */
  .flex-col {
    flex-direction: column;
  }

  /* Adjust button order for mobile UX */
  .order-1 {
    order: 1;
  }

  .order-2 {
    order: 2;
  }
}

/* ✅ High Contrast Mode Support */
@media (prefers-contrast: high) {
  .bg-white {
    background-color: white;
    border: 2px solid black;
  }

  .text-gray-600 {
    color: black;
  }
}

/* ✅ Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.2s ease;
  }

  .modal-enter-from,
  .modal-leave-to {
    opacity: 0;
    transform: none;
  }

  button {
    transition: none;
  }

  button:hover:not(:disabled) {
    transform: none;
  }

  .animate-spin {
    animation: none;
  }
}

/* ✅ Enhanced CSS Containment for Performance */
.modal-content {
  contain: layout style paint;
  content-visibility: auto;
  contain-intrinsic-size: 400px 300px;
}

/* ✅ GPU Acceleration for Smooth Animations */
.fixed {
  transform: translateZ(0);
  will-change: transform;
}

/* ✅ Optimized Backdrop */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* ✅ Focus Visible Enhancement */
button:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* ✅ Loading State Optimizations */
button:disabled {
  pointer-events: none;
  user-select: none;
}

/* ✅ Smooth Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ✅ Print Styles */
@media print {
  .fixed {
    position: static;
    background: white;
    color: black;
  }

  .backdrop-blur-sm {
    backdrop-filter: none;
    background: white;
  }
}
</style>
