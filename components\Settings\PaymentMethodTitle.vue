<template>
  <div
    class="flex items-center justify-between py-3 px-4 ml-14 border-l-2 border-blue-200"
  >
    <!-- Left Side: Toggle + Icon + Account Info -->
    <div class="flex items-center gap-3">
      <!-- Account Toggle -->
      <ToggleSwitch
        :model-value="title.isActive !== false"
        size="sm"
        @change="handleToggle"
      />
      {{ title }}
      <!-- Account Icon -->
      <div
        class="w-6 h-6 rounded flex items-center justify-center bg-white border border-gray-200"
      >
        <svg
          class="w-3 h-3 text-gray-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
          />
        </svg>
      </div>

      <!-- Account Info -->
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-2">
          <span class="text-sm font-medium text-gray-900">
            {{ title.name || title.id }}
          </span>
          <span
            v-if="title.isDefault"
            class="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded"
          >
            Mặc định
          </span>
        </div>

        <!-- Display all properties from title object -->
        <div class="space-y-1">
          <div
            v-for="(value, key) in displayableProperties"
            :key="key"
            class="flex items-center gap-2 text-xs text-gray-600"
          >
            <span class="font-medium capitalize min-w-[80px]">
              {{ formatPropertyName(key) }}:
            </span>
            <span class="text-gray-800">{{ formatPropertyValue(value) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side: Actions -->
    <div class="flex items-center gap-2">
      <button @click="handleEdit" class="p-1 text-gray-400 hover:text-gray-600">
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      </button>

      <button class="p-1 text-gray-400 hover:text-gray-600">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface BeneficiaryTitle {
  id: string;
  name: string;
  accountNumber?: string;
  bankName?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface Props {
  title: BeneficiaryTitle;
  methodId: string;
}

interface Emits {
  (e: "toggle", titleId: string, value: boolean): void;
  (e: "edit", titleId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Helper functions for displaying title properties
const displayableProperties = computed(() => {
  const excludeKeys = ["id", "name", "isDefault"]; // Keys to exclude from display
  const result: Record<string, any> = {};

  Object.entries(props.title).forEach(([key, value]) => {
    if (
      !excludeKeys.includes(key) &&
      value !== null &&
      value !== undefined &&
      value !== ""
    ) {
      result[key] = value;
    }
  });

  return result;
});

const formatPropertyName = (key: string): string => {
  const nameMap: Record<string, string> = {
    accountNumber: "Số TK",
    bankName: "Ngân hàng",
    bankCode: "Mã NH",
    isActive: "Trạng thái",
    accountName: "Tên TK",
    balance: "Số dư",
    currency: "Tiền tệ",
    type: "Loại",
    status: "Tình trạng",
    createdAt: "Ngày tạo",
    updatedAt: "Cập nhật",
  };

  return nameMap[key] || key.replace(/([A-Z])/g, " $1").trim();
};

const formatPropertyValue = (value: any): string => {
  if (typeof value === "boolean") {
    return value ? "Có" : "Không";
  }

  if (typeof value === "number") {
    return value.toLocaleString("vi-VN");
  }

  if (typeof value === "string" && value.includes("T") && value.includes("Z")) {
    // Likely a date string
    try {
      return new Date(value).toLocaleDateString("vi-VN");
    } catch {
      return value;
    }
  }

  return String(value);
};

const handleToggle = (value: boolean) => {
  emit("toggle", props.title.id, value);
};

const handleEdit = () => {
  emit("edit", props.title.id);
};
</script>
