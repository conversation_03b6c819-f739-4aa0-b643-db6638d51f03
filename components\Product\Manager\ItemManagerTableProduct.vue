<template>
  <!-- Sản phẩm -->
  <div class="col-span-4 flex items-center">
    <div class="flex items-center gap-3 w-full">
      <div class="flex-shrink-0">
        <NuxtImg
          :src="urlImage"
          alt="Product Image"
          class="w-12 h-12 rounded-lg object-contain border border-gray-200"
          loading="lazy"
        />
      </div>
      <div class="flex-1 min-w-0">
        <input
          type="text"
          v-model="name"
          class="w-full p-1 text-sm font-medium focus:outline-none focus:border-gray-400 focus:border-b focus:border-dashed"
          @change="handleChangeNameProduct"
        />
        <div class="text-xs text-gray-500 mt-1 space-y-1">
          <div><span class="font-medium">ID:</span> {{ product?.id }}</div>
          <div>
            <span class="font-medium">SKU:</span> {{ product?.sku || "N/A" }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Danh mục -->
  <div class="col-span-2 flex items-center relative" ref="dropdownRef">
    <div class="w-full">
      <!-- Tag đã chọn -->
      <div
        class="flex items-center gap-1 border border-gray-300 rounded p-2 h-[40px] cursor-text text-sm overflow-hidden"
        @click="showDropdown = true"
      >
        <!-- Hiển thị tối đa 2 danh mục -->
        <template v-if="product?.categories?.length > 0">
          <!-- Danh mục đầu tiên -->
          <span
            v-if="product.categories[0]"
            class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs whitespace-nowrap flex items-center gap-1 max-w-[80px]"
            :title="product.categories[0]?.title"
          >
            <span class="truncate">{{ product.categories[0]?.title }}</span>
            <button
              @click.stop="removeCategory(product.categories[0].id)"
              class="text-xs font-bold hover:text-red-500 flex-shrink-0"
              type="button"
              title="Xóa danh mục"
            >
              ×
            </button>
          </span>

          <!-- Danh mục thứ hai (nếu có) -->
          <span
            v-if="product.categories[1] && product.categories.length === 2"
            class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs whitespace-nowrap flex items-center gap-1 max-w-[80px]"
            :title="product.categories[1]?.title"
          >
            <span class="truncate">{{ product.categories[1]?.title }}</span>
            <button
              @click.stop="removeCategory(product.categories[1].id)"
              class="text-xs font-bold hover:text-red-500 flex-shrink-0"
              type="button"
              title="Xóa danh mục"
            >
              ×
            </button>
          </span>

          <!-- Hiển thị "+X khác" nếu có nhiều hơn 2 danh mục -->
          <span
            v-if="product.categories.length > 2"
            class="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs whitespace-nowrap"
            :title="remainingCategoriesText"
          >
            +{{ product.categories.length - 1 }} khác
          </span>

          <!-- Hiển thị chỉ danh mục thứ hai nếu có nhiều hơn 2 -->
          <span
            v-else-if="product.categories[1] && product.categories.length > 2"
            class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs whitespace-nowrap flex items-center gap-1 max-w-[60px]"
          >
            <span class="truncate">{{ product.categories[1]?.title }}</span>
            <button
              @click.stop="removeCategory(product.categories[1].id)"
              class="text-xs font-bold hover:text-red-500 flex-shrink-0"
              type="button"
            >
              ×
            </button>
          </span>
        </template>

        <!-- Placeholder khi chưa có danh mục -->
        <span v-else class="text-gray-400 text-xs">Chọn danh mục...</span>
      </div>

      <!-- Dropdown -->
      <div
        v-if="showDropdown"
        class="absolute z-20 mt-1 w-full bg-white border rounded shadow-lg max-h-40 overflow-y-auto"
      >
        <!-- Hiển thị tất cả danh mục đã chọn để có thể xóa -->
        <div
          v-if="product?.categories?.length > 0"
          class="border-b border-gray-200"
        >
          <div class="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50">
            Danh mục đã chọn:
          </div>
          <div
            v-for="category in product.categories"
            :key="'selected-' + category.id"
            class="px-3 py-2 hover:bg-red-50 cursor-pointer text-sm flex items-center justify-between group"
            @click="removeCategory(category.id)"
          >
            <span>{{ category.title }}</span>
            <span class="text-red-500 text-xs group-hover:font-bold">Xóa</span>
          </div>
        </div>

        <!-- Danh mục có thể thêm -->
        <div v-if="filteredCategories.length > 0">
          <div class="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50">
            Thêm danh mục:
          </div>
          <div
            v-for="category in filteredCategories"
            :key="category.id"
            @click="handleSelectCategory(category.id)"
            class="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
          >
            {{ category.title }}
          </div>
        </div>

        <!-- Thông báo khi không có danh mục để thêm -->
        <div
          v-else-if="product?.categories?.length > 0"
          class="px-3 py-2 text-sm text-gray-500"
        >
          Không có danh mục khác để thêm
        </div>
      </div>
    </div>
  </div>

  <!-- Giá bán -->
  <div class="col-span-2 flex items-center justify-center">
    <CurrencyInput
      class="outline-none p-1 rounded text-center text-primary font-semibold focus:border-b focus:border-dashed focus:border-gray-300 w-full text-sm"
      v-model="price"
      :options="{
        currency: 'VND',
        currencyDisplay: 'hidden',
        hideCurrencySymbolOnFocus: false,
        hideGroupingSeparatorOnFocus: false,
        hideNegligibleDecimalDigitsOnFocus: false,
      }"
      @change="handleUpdatePrice"
    />
  </div>

  <!-- Giá KM -->
  <div class="col-span-2 flex items-center justify-center">
    <CurrencyInput
      class="outline-none p-1 rounded text-center text-green-600 font-semibold focus:border-b focus:border-dashed focus:border-gray-300 w-full text-sm"
      v-model="pricePromotion"
      :options="{
        currency: 'VND',
        currencyDisplay: 'hidden',
        hideCurrencySymbolOnFocus: false,
        hideGroupingSeparatorOnFocus: false,
        hideNegligibleDecimalDigitsOnFocus: false,
      }"
      @change="handleUpdatePricePromotion"
    />
  </div>

  <!-- Đơn vị -->
  <div class="col-span-1 flex items-center">
    <select
      v-model="unit"
      class="focus:outline-none bg-transparent border border-gray-300 rounded p-1 w-full text-sm"
      @change="handleUpdateUnit"
    >
      <option v-for="unit in dataUnit" :value="unit?.id">
        {{ unit?.name }}
      </option>
    </select>
  </div>

  <!-- Thao tác -->
  <div class="col-span-1 flex items-center justify-center">
    <button
      @click="handleViewDetail"
      class="inline-flex items-center justify-center w-8 h-8 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
      title="Xem chi tiết"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-4 h-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
        />
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
        />
      </svg>
    </button>
  </div>
</template>
<script setup>
const props = defineProps(["product", "dataUnit", "dataCategories"]);
const route = useRoute();
const handleNavigate = () => {
  navigateTo(
    `/product/detail?productId=${props.product?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};
const { getImageProducrUrl } = usePortal();
const urlImage = ref();
const price = ref(0);
const pricePromotion = ref(0);
const unit = ref();
const name = ref();

onMounted(async () => {
  name.value = props.product.title;
  unit.value = props.product.unitDTO?.id;
  if (props.product?.compareAtPrice) {
    price.value = props.product?.compareAtPrice;
    pricePromotion.value = props.product?.price;
  } else {
    price.value = props.product?.price;
    pricePromotion.value = "0";
  }
  urlImage.value = await getImageProducrUrl(props.product?.id, "PRODUCT");
});
const {
  updateProductTitle,
  getCategory,
  updateCategory,
  updateShortDescription,
  updatePrice,
  updatePricePromotion,
  updateUnit,
} = useProduct();
const emits = defineEmits(["updateProduct", "viewDetail"]);
const auth = useCookie("auth");

const handleViewDetail = () => {
  emits("viewDetail", props.product?.id);
};

const handleChangeNameProduct = async () => {
  await updateProductTitle(props.product.id, name.value, auth.value?.user?.id);
  emits("updateProduct", props.product?.id);
};
const handleUpdatePrice = async () => {
  await updatePrice(props.product?.id, price.value, auth.value?.user?.id);
  emits("updateProduct", props.product?.id);
};
const handleUpdatePricePromotion = async () => {
  if (pricePromotion.value > price.value) {
    useNuxtApp().$toast.error("Giá khuyến mãi phải nhỏ hơn giá bán");
    return;
  }
  await updatePricePromotion(
    props.product.id,
    pricePromotion.value,
    auth.value?.user?.id
  );
  emits("updateProduct", props.product?.id);
};
const handleUpdateUnit = async () => {
  await updateUnit(props.product?.id, unit.value, auth.value?.user?.id);
  emits("updateProduct", props.product?.id);
};
const showDropdown = ref(false);

// Lọc danh mục chưa chọn
const filteredCategories = computed(() =>
  props.dataCategories.filter(
    (cat) =>
      !props.product.categories?.some((selected) => selected.id === cat.id)
  )
);

// Text hiển thị cho các danh mục còn lại
const remainingCategoriesText = computed(() => {
  if (props.product?.categories?.length > 2) {
    const remainingCategories = props.product.categories.slice(1);
    return remainingCategories.map((cat) => cat.title).join(", ");
  }
  return "";
});

const removeCategory = async (id) => {
  await updateCategory(props.product?.id, id, auth.value?.user?.id);
  emits("updateProduct", props.product?.id);
};
onMounted(() => {
  document.addEventListener("click", onClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener("click", onClickOutside);
});
const dropdownRef = ref(null);

const onClickOutside = (e) => {
  const target = e.target;
  if (dropdownRef.value && !dropdownRef.value.contains(target)) {
    showDropdown.value = false;
  }
};

const handleSelectCategory = async (categoryId) => {
  await updateCategory(props.product?.id, categoryId, auth.value?.user?.id);
  emits("updateProduct", props.product?.id);

  showDropdown.value = false;
};
</script>
