<template>
  <div class="flex flex-col m-0 px-2 pb-2 bg-white rounded">
    <div class="flex items-center gap-1">
      <span class="text-primary">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
          />
        </svg>
      </span>
      <h3 class="text-sm font-semibold text-primary">Ghi chú</h3>
    </div>

    <div class="grid grid-cols-1 gap-2 mt-2 animate-popup" v-if="isShow">
      <div class="flex flex-col gap-2">
        <textarea
          rows="2"
          id="note"
          v-model="orderDescription"
          class="py-1 px-2 w-full md:text-sm text-base rounded outline-none border bg-secondary"
          placeholder="Ghi chú đơn hàng"
          @blur="updateOrderDescription"
          :disabled="isNotDraft || !orderStore.orderDetail"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useOrderStore } from "@/stores/order";

const orderStore = useOrderStore();
const isNotDraft = computed(() => orderStore.isNotDraft);

const note = ref("");

const orderDescription = computed({
  get() {
    return orderStore.orderDetail?.order?.note || "";
  },
  set(value) {
    note.value = value;
  },
});

const isShow = ref(true);

const toggleContent = () => {
  isShow.value = !isShow.value;
};

const updateOrderDescription = () => {
  orderStore.updateDescriptionToOder(note.value);
};
</script>
