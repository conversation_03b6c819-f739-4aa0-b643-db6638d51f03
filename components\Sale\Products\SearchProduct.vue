<template>
  <div :class="containerClasses">
    <!-- Header Section -->
    <header class="flex items-center justify-between">
      <h3 class="text-sm font-semibold text-primary flex items-center gap-1">
        <svg
          class="w-4 h-4 text-primary"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
          />
        </svg>
        {{ isSwapOrder ? "Sản phẩm đổi" : "Sản phẩm" }}
      </h3>
      <button
        type="button"
        @click="handleOpenCategory"
        class="text-sm font-semibold text-primary cursor-pointer underline decoration-primary decoration-1 hover:opacity-80 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded flex items-center gap-2"
        aria-label="Mở danh mục sản phẩm"
      >
        <svg
          class="w-4 h-4 text-primary"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
        Danh mục sản phẩm
      </button>
    </header>

    <!-- Search Section -->
    <div class="relative w-full mt-2 mb-1 bg-bgGray rounded-2xl">
      <div class="relative w-full">
        <input
          ref="searchInput"
          v-model="keyword"
          type="text"
          :class="searchInputClasses"
          placeholder="Tìm kiếm theo tên sản phẩm, id, sku, ...."
          @input="debounceSearch"
          @keydown.enter="handleGetProduct"
          @keydown.escape="clearInputSearch"
          aria-label="Tìm kiếm sản phẩm"
          autocomplete="off"
        />

        <!-- Clear Button -->
        <Transition
          enter-active-class="transition-opacity duration-200"
          leave-active-class="transition-opacity duration-200"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <button
            v-if="!searchLoading && keyword"
            type="button"
            @click="clearInputSearch"
            :class="clearButtonClasses"
            aria-label="Xóa từ khóa tìm kiếm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </button>
        </Transition>

        <!-- Loading Spinner - Chuẩn theo project -->
        <Transition
          enter-active-class="transition-opacity duration-200"
          leave-active-class="transition-opacity duration-200"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <div
            v-if="searchLoading"
            class="animate-spin rounded-full h-3 w-3 border-b-2 border-primary absolute top-3 right-[55px]"
            aria-label="Đang tìm kiếm"
          ></div>
        </Transition>

        <!-- Search Button -->
        <button
          id="searchProductBtn"
          type="button"
          @click="handleGetProduct"
          :disabled="!keyword.trim() || searchLoading"
          :class="searchButtonClasses"
          class="text-white"
          aria-label="Tìm kiếm sản phẩm"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </button>

        <!-- Product List -->
        <ProductList
          v-if="products.length && isModalOpen"
          :products="products"
          :isOpen="isModalOpen"
          @close="closeModal"
        />
      </div>
    </div>

    <!-- Error Message -->
    <Transition
      enter-active-class="transition-all duration-300"
      leave-active-class="transition-all duration-300"
      enter-from-class="opacity-0 transform -translate-y-1"
      leave-to-class="opacity-0 transform -translate-y-1"
    >
      <div
        v-if="isProduct"
        class="text-sm text-red-400 mt-1"
        role="alert"
        aria-live="polite"
      >
        Không tìm thấy sản phẩm mà bạn đang tìm kiếm
      </div>
    </Transition>
  </div>

  <!-- Category Popup -->
  <CategoryProductPopup
    v-if="isOpenCategory"
    @cancel="handleOpenCategory"
    @confirm="handleOpenCategory"
  />
</template>

<script setup>
// Lazy load heavy components
const ProductList = defineAsyncComponent(() =>
  import("~/components/Sale/Products/ProductList.vue")
);

const CategoryProductPopup = defineAsyncComponent(() =>
  import("~/components/Category/CategoryProductPopup.vue")
);

// Props
const props = defineProps({
  isSwapOrder: {
    type: Boolean,
    default: false,
  },
});

// Composables
const { searchLoading, getProducts } = useProduct();

// Reactive state
const timeoutId = ref(null);
const keyword = ref("");
const products = ref([]);
const isModalOpen = ref(false);
const isProduct = ref(false);
const isOpenCategory = ref(false);
const searchInput = ref(null);

// Computed classes for better performance and maintainability
const containerClasses = computed(() => ({
  "py-2": props.isSwapOrder,
  "p-2": !props.isSwapOrder,
}));

const searchInputClasses = computed(() => [
  "w-full py-2 px-2 text-base outline-none bg-secondary rounded-md pr-[120px] md:text-sm",
  "border-0 focus:ring-2 focus:ring-primary focus:ring-opacity-50",
  "transition-all duration-200",
  "placeholder:text-gray-400",
]);

const clearButtonClasses = computed(() => [
  "absolute top-3 right-[55px] cursor-pointer w-4 h-4 text-red-500",
  "hover:text-red-600 transition-colors duration-200",
  "focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded",
]);

const searchButtonClasses = computed(() => [
  "absolute top-1/2 -translate-y-1/2 right-0 h-full py-[11px] px-1",
  "flex justify-center items-center bg-primary w-[50px] rounded-r-md",
  "cursor-pointer transition-all duration-200",
  "hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50",
  "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary",
  "stroke-white",
]);

// Methods
const closeModal = () => {
  isModalOpen.value = false;
};

const clearInputSearch = () => {
  keyword.value = "";
  isModalOpen.value = false;
  isProduct.value = false;
  searchInput.value?.focus();
};

const handleGetProduct = async () => {
  const trimmedKeyword = keyword.value.trim();
  if (!trimmedKeyword || searchLoading.value) return;

  try {
    searchLoading.value = true;
    products.value = await getProducts({ keyword: trimmedKeyword });
    isModalOpen.value = true;
    isProduct.value = products.value.length === 0;
  } catch (error) {
    console.error("Error searching products:", error);
    isProduct.value = true;
  } finally {
    searchLoading.value = false;
  }
};

const debounceSearch = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }

  const trimmedKeyword = keyword.value.trim();
  if (!trimmedKeyword) {
    isModalOpen.value = false;
    isProduct.value = false;
    return;
  }

  timeoutId.value = setTimeout(async () => {
    await handleGetProduct();
  }, 500);
};

const handleOpenCategory = () => {
  isOpenCategory.value = !isOpenCategory.value;
};

// Cleanup on unmount
onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }
});

// Keyboard shortcuts
onMounted(() => {
  const handleKeydown = (event) => {
    if (event.ctrlKey && event.key === "k") {
      event.preventDefault();
      searchInput.value?.focus();
    }
  };

  document.addEventListener("keydown", handleKeydown);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown);
  });
});
</script>

<style scoped></style>
