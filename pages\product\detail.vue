<template>
  <div class="h-[calc(100vh-56px)] mx-1 sm:mx-2">
    <!-- Main Content -->
    <div class="h-full flex flex-col">
      <!-- Content Area -->
      <div class="flex-1 overflow-hidden">
        <!-- Desktop Layout -->
        <div class="hidden lg:block h-full">
          <div class="h-full grid grid-cols-12 gap-2">
            <!-- Left Column - Product Details -->
            <div class="col-span-8 space-y-3  overflow-y-auto">
              <ProductManagerDetail
                :product="productStore?.productDetail"
                @update="handleProductUpdate"
              />
              <ProductPriceManager
                :product="productStore?.productDetail"
                @update="handleProductUpdate"
              />
              <ProductUnitManager
                :product="productStore?.productDetail"
                @update="handleProductUpdate"
              />
              <ProductOption
                :product="productStore?.productDetail"
                @update="handleProductUpdate"
              />
            </div>

            <!-- Right Column - Image Management -->
            <div class="col-span-4">
              <div class="sticky top-0">
                <ProductImageManager
                  :product="productStore?.productDetail"
                  @update="handleProductUpdate"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Layout -->
        <div
          class="lg:hidden h-full overflow-y-auto scroll-smooth overscroll-y-contain"
        >
          <!-- Mobile Content - All scrollable -->
          <div class="space-y-3 pb-6">
            <!-- Mobile Image Section -->
            <ProductImageManager
              :product="productStore?.productDetail"
              @update="handleProductUpdate"
            />

            <!-- Mobile Content -->
            <ProductManagerDetail
              :product="productStore?.productDetail"
              @update="handleProductUpdate"
            />
            <ProductPriceManager
              :product="productStore?.productDetail"
              @update="handleProductUpdate"
            />
            <ProductUnitManager
              :product="productStore?.productDetail"
              @update="handleProductUpdate"
            />
            <ProductOption
              :product="productStore?.productDetail"
              @update="handleProductUpdate"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Chi tiết sản phẩm",
});

useHead({
  title: "Chi tiết sản phẩm",
  meta: [
    {
      name: "description",
      content: "Chi tiết sản phẩm",
    },
  ],
});

const route = useRoute();
const router = useRouter();
const productStore = useProductStore();
const isLoading = ref(false);

// Navigation handlers
const handleGoBack = () => {
  router.back();
};

const handleSaveChanges = async () => {
  // TODO: Implement save changes logic
  console.log("Saving changes...");
};

const handlePreview = () => {
  // TODO: Implement preview logic
  console.log("Opening preview...");
};

// Product update handler
const handleProductUpdate = async () => {
  // Refresh product data after update
  if (route.query.productId) {
    await productStore.getProductByProductId(route.query.productId as string);
  }
};

// Watch for product ID changes
watch(
  () => route.query.productId,
  async (newVal) => {
    if (newVal) {
      isLoading.value = true;
      try {
        await productStore.getProductByProductId(newVal as string);
      } catch (error) {
        console.error("Error loading product:", error);
      } finally {
        isLoading.value = false;
      }
    }
  },
  { immediate: true }
);
</script>
