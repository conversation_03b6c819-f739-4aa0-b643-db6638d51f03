<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        <PERSON><PERSON><PERSON> <PERSON>ì<PERSON> phương thức thanh toán
      </h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các ph<PERSON><PERSON><PERSON> thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>
    <!-- Payment Method List (Toggle Style) -->
    <div class="bg-white rounded border border-gray-200">
      <div
        class="flex items-center justify-between p-4 border-b border-gray-200"
      >
        <h3 class="text-base font-medium text-gray-900">
          Danh sách phương thức thanh toán
        </h3>
        <div class="flex items-center gap-3">
          <button
            @click="handleGetPaymentMethods"
            :disabled="isLoadingPaymentMethods"
            class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1 disabled:opacity-50"
          >
            <svg
              class="w-4 h-4"
              :class="{ 'animate-spin': isLoadingPaymentMethods }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            {{ isLoadingPaymentMethods ? "Đang tải..." : "Làm mới API" }}
          </button>
        </div>
      </div>

      <!-- Payment Methods List -->
      <div class="divide-y divide-gray-200">
        <!-- API Payment Methods -->
        <ApiPaymentMethodItem
          v-for="method in apiPaymentMethods"
          :key="method.id"
          :method="method"
          :is-expanded="expandedApiMethods.includes(method.id)"
          @toggle-expanded="toggleApiMethod(method.id)"
          @method-toggle="handleApiMethodToggle(method, $event)"
          @title-toggle="
            (titleId, value) => handleTitleToggle(method.id, titleId, value)
          "
          @title-edit="editApiTitle(method.id, $event)"
        />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

// Reactive data

// API Payment Methods data
const apiPaymentMethods = ref<ApiPaymentMethod[]>([]);
const isLoadingPaymentMethods = ref(false);
const expandedApiMethods = ref<string[]>([]);

// Methods

// API Payment Methods handlers
const toggleApiMethod = (methodId: string) => {
  const index = expandedApiMethods.value.indexOf(methodId);
  if (index > -1) {
    expandedApiMethods.value.splice(index, 1);
  } else {
    expandedApiMethods.value.push(methodId);
  }
};

const handleApiMethodToggle = (method: ApiPaymentMethod, value: boolean) => {};

const handleTitleToggle = (
  methodId: string,
  titleId: string,
  value: boolean
) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  if (title) {
    title.isActive = value;
  }
};

const editApiTitle = (methodId: string, titleId: string) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
};

//
const { paymentMethods } = usePayment();
const handleGetPaymentMethods = async () => {
  try {
    isLoadingPaymentMethods.value = true;
    const response = await paymentMethods();

    apiPaymentMethods.value = response || [];

    isLoadingPaymentMethods.value = false;
  } catch (error) {
    isLoadingPaymentMethods.value = false;
    throw error;
  }
};

onMounted(async () => {
  await handleGetPaymentMethods();
});
</script>
