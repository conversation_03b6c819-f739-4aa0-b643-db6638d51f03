<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        <PERSON><PERSON><PERSON> <PERSON>ì<PERSON> phương thức thanh toán
      </h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các ph<PERSON><PERSON><PERSON> thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>
    <!-- Payment Method List (Toggle Style) -->
    <div class="bg-white rounded border border-gray-200">
      <div
        class="flex items-center justify-between p-4 border-b border-gray-200"
      >
        <h3 class="text-base font-medium text-gray-900">
          Danh sách phương thức thanh toán
        </h3>
        <div class="flex items-center gap-3">
          <button
            @click="handleGetPaymentMethods"
            :disabled="isLoadingPaymentMethods"
            class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1 disabled:opacity-50"
          >
            <svg
              class="w-4 h-4"
              :class="{ 'animate-spin': isLoadingPaymentMethods }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            {{ isLoadingPaymentMethods ? "Đang tải..." : "Làm mới API" }}
          </button>
          <button
            @click="showAddPaymentModal = true"
            class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
            Thêm phương thức
          </button>
        </div>
      </div>

      <!-- Payment Methods List -->
      <div class="divide-y divide-gray-200">
        <!-- API Payment Methods -->
        <div v-for="method in apiPaymentMethods" :key="method.id" class="group">
          <!-- Payment Method Row -->
          <div class="flex items-center justify-between p-4 hover:bg-gray-50">
            <!-- Left Side: Toggle + Icon + Name -->
            <div class="flex items-center gap-3">
              <!-- Toggle Switch -->
              <ToggleSwitch
                :model-value="true"
                size="md"
                @change="handleApiMethodToggle(method, $event)"
              />

              <!-- Method Icon -->
              <div
                class="w-8 h-8 rounded-lg flex items-center justify-center bg-gray-100"
              >
                <img
                  v-if="method.image"
                  :src="method.image"
                  :alt="method.name"
                  class="w-6 h-6 object-contain rounded"
                />
                <svg
                  v-else
                  class="w-6 h-6 text-gray-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                  />
                </svg>
              </div>

              <!-- Method Name -->
              <div>
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium text-gray-900">
                    {{ method.name }}
                  </span>
                  <span
                    class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"
                  >
                    {{ method.code }}
                  </span>
                </div>
                <p class="text-xs text-gray-500">{{ method.description }}</p>
              </div>
            </div>

            <!-- Right Side: Actions -->
            <div class="flex items-center gap-3">
              <!-- Expand/Collapse Button -->
              <button
                @click="toggleApiMethod(method.id)"
                class="p-1 text-gray-400 hover:text-gray-600"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  :class="{
                    'rotate-90': expandedApiMethods.includes(method.id),
                  }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>

              <!-- More Actions -->
              <button class="p-1 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                  />
                </svg>
              </button>
            </div>
          </div>

          <!-- Expanded Titles (Beneficiary Accounts) -->
          <div
            v-if="
              expandedApiMethods.includes(method.id) &&
              method.titles &&
              method.titles.length > 0
            "
            class="bg-gray-50 border-t border-gray-200"
          >
            <div
              v-for="title in method.titles"
              :key="title.id"
              class="flex items-center justify-between py-3 px-4 ml-14 border-l-2 border-blue-200"
            >
              <!-- Left Side: Toggle + Icon + Account Info -->
              <div class="flex items-center gap-3">
                <!-- Account Toggle -->
                <ToggleSwitch
                  :model-value="title.isActive !== false"
                  size="sm"
                  @change="handleTitleToggle(method.id, title.id, $event)"
                />

                <!-- Account Icon -->
                <div
                  class="w-6 h-6 rounded flex items-center justify-center bg-white border border-gray-200"
                >
                  <svg
                    class="w-3 h-3 text-gray-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                    />
                  </svg>
                </div>

                <!-- Account Info -->
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-medium text-gray-900">
                      {{ title.name || title.id }}
                    </span>
                    <span
                      v-if="title.isDefault"
                      class="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded"
                    >
                      Mặc định
                    </span>
                  </div>

                  <!-- Display all properties from title object -->
                  <div class="space-y-1">
                    <div
                      v-for="(value, key) in getDisplayableProperties(title)"
                      :key="key"
                      class="flex items-center gap-2 text-xs text-gray-600"
                    >
                      <span class="font-medium capitalize min-w-[80px]"
                        >{{ formatPropertyName(key) }}:</span
                      >
                      <span class="text-gray-800">{{
                        formatPropertyValue(value)
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Side: Actions -->
              <div class="flex items-center gap-2">
                <button
                  @click="editApiTitle(method.id, title.id)"
                  class="p-1 text-gray-400 hover:text-gray-600"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </button>

                <button class="p-1 text-gray-400 hover:text-gray-600">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- No Titles Message -->
          <div
            v-else-if="
              expandedApiMethods.includes(method.id) &&
              (!method.titles || method.titles.length === 0)
            "
            class="bg-gray-50 border-t border-gray-200 px-4 py-3 ml-14"
          >
            <p class="text-xs text-gray-500 italic">
              Không có tài khoản thụ hưởng
            </p>
          </div>
        </div>

        <!-- Original Tree Structure -->
        <div
          v-for="(category, categoryIndex) in paymentTree"
          :key="categoryIndex"
          class="group"
        >
          <!-- Category Row -->
          <div class="flex items-center justify-between p-4 hover:bg-gray-50">
            <!-- Left Side: Toggle + Icon + Name -->
            <div class="flex items-center gap-3">
              <!-- Toggle Switch -->
              <ToggleSwitch
                v-model="category.isActive"
                size="md"
                @change="handleCategoryToggle(categoryIndex, $event)"
              />

              <!-- Category Icon -->
              <div
                class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-xs font-bold"
                :class="category.color"
              >
                {{ category.icon }}
              </div>

              <!-- Category Name -->
              <span class="text-sm font-medium text-gray-900">
                {{ category.name }}
              </span>
            </div>

            <!-- Right Side: Default Badge + Actions -->
            <div class="flex items-center gap-3">
              <span
                v-if="category.hasDefault"
                class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded"
              >
                Mặc định
              </span>

              <!-- Expand/Collapse Button -->
              <button
                @click="toggleCategory(categoryIndex)"
                class="p-1 text-gray-400 hover:text-gray-600"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  :class="{ 'rotate-90': category.expanded }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>

              <!-- More Actions -->
              <button class="p-1 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                  />
                </svg>
              </button>
            </div>
          </div>

          <!-- Expanded Methods -->
          <div
            v-if="category.expanded"
            class="bg-gray-50 border-t border-gray-200"
          >
            <div
              v-for="(method, methodIndex) in category.methods"
              :key="methodIndex"
              class="flex items-center justify-between py-3 px-4 ml-14 border-l-2 border-blue-200"
            >
              <!-- Left Side: Toggle + Icon + Method Info -->
              <div class="flex items-center gap-3">
                <!-- Method Toggle -->
                <ToggleSwitch
                  v-model="method.isActive"
                  size="sm"
                  @change="
                    handleMethodToggle(categoryIndex, methodIndex, $event)
                  "
                />

                <!-- Method Icon -->
                <div
                  class="w-6 h-6 rounded flex items-center justify-center bg-white border border-gray-200"
                >
                  <svg
                    class="w-3 h-3 text-gray-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                    />
                  </svg>
                </div>

                <!-- Method Info -->
                <div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-900">{{
                      method.name
                    }}</span>
                    <span
                      v-if="method.isDefault"
                      class="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded"
                    >
                      Mặc định
                    </span>
                  </div>
                  <p class="text-xs text-gray-500">{{ method.description }}</p>
                </div>
              </div>

              <!-- Right Side: Actions -->
              <div class="flex items-center gap-2">
                <button
                  @click="editTreeMethod(categoryIndex, methodIndex)"
                  class="p-1 text-gray-400 hover:text-gray-600"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                </button>

                <button class="p-1 text-gray-400 hover:text-gray-600">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Add Method Button -->
            <div class="px-4 pb-3 ml-14">
              <button
                @click="showAddMethodToCategory(categoryIndex)"
                class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Thêm phương thức
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Add Payment Method Modal -->
    <Teleport to="body">
      <div
        v-if="showAddPaymentModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Thêm phương thức thanh toán
          </h3>

          <form @submit.prevent="addPaymentMethod" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Loại phương thức</label
              >
              <select
                v-model="newPaymentMethod.type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                <option value="">Chọn loại</option>
                <option value="bank">Chuyển khoản ngân hàng</option>
                <option value="momo">Ví MoMo</option>
                <option value="zalopay">ZaloPay</option>
                <option value="vnpay">VNPay</option>
                <option value="cash">Tiền mặt</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Tên phương thức</label
              >
              <input
                v-model="newPaymentMethod.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="VD: Chuyển khoản Vietcombank"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Mô tả</label
              >
              <textarea
                v-model="newPaymentMethod.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Mô tả chi tiết về phương thức thanh toán"
              ></textarea>
            </div>

            <div v-if="newPaymentMethod.type === 'bank'">
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Số tài khoản</label
              >
              <input
                v-model="newPaymentMethod.accountNumber"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Nhập số tài khoản"
              />
            </div>

            <div class="flex items-center">
              <input
                v-model="newPaymentMethod.isActive"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Kích hoạt ngay</label>
            </div>

            <div class="flex items-center">
              <input
                v-model="newPaymentMethod.isDefault"
                type="checkbox"
                class="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label class="ml-2 text-sm text-gray-700">Đặt làm mặc định</label>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddPaymentModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90"
              >
                Thêm
              </button>
            </div>
          </form>
        </div>
      </div>
    </Teleport>
    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces
interface PaymentMethod {
  type: string;
  name: string;
  description: string;
  accountNumber?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface BeneficiaryTitle {
  id: string;
  name: string;
  accountNumber?: string;
  bankName?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface BeneficiaryAccount {
  bankCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  isActive: boolean;
  isDefault: boolean;
}

interface TreeMethod {
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  details?: Record<string, string>;
}

interface PaymentCategory {
  name: string;
  icon: string;
  color: string;
  isActive: boolean;
  expanded: boolean;
  methods: TreeMethod[];
  hasDefault?: boolean;
}

// Reactive data
const showAddPaymentModal = ref(false);
const showAddBeneficiaryModal = ref(false);
const showAddCategoryModal = ref(false);

// API Payment Methods data
const apiPaymentMethods = ref<ApiPaymentMethod[]>([]);
const isLoadingPaymentMethods = ref(false);
const expandedApiMethods = ref<string[]>([]);

// Sample data
const paymentMethod = ref<PaymentMethod[]>([
  {
    type: "bank",
    name: "Chuyển khoản Vietcombank",
    description: "Chuyển khoản qua ngân hàng Vietinbank",
    accountNumber: "************",
    isActive: true,
    isDefault: true,
  },
  {
    type: "momo",
    name: "Ví MoMo",
    description: "Thanh toán qua ví điện tử MoMo",
    isActive: true,
    isDefault: false,
  },
]);

const beneficiaryAccounts = ref<BeneficiaryAccount[]>([
  {
    bankCode: "CTG",
    bankName: "Viettinbank",
    accountNumber: "************",
    accountName: "DO NGOC DUY HUNG",
    isActive: true,
    isDefault: true,
  },
]);

// Payment Tree Structure
const paymentTree = ref<PaymentCategory[]>([
  {
    name: "Tiền mặt",
    icon: "💵",
    color: "bg-green-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
  {
    name: "Chuyển khoản",
    icon: "🏦",
    color: "bg-blue-600",
    isActive: true,
    expanded: true,
    hasDefault: true,
    methods: [
      {
        name: "Do Ngoc Duy Hung",
        description: "VietinBank - Ngân hàng TMCP Công thương Việt Nam",
        isActive: true,
        isDefault: true,
      },
    ],
  },
  {
    name: "Thanh toán thẻ",
    icon: "💳",
    color: "bg-purple-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
]);

// Form data
const newPaymentMethod = ref<PaymentMethod>({
  type: "",
  name: "",
  description: "",
  accountNumber: "",
  isActive: true,
  isDefault: false,
});

const newBeneficiaryAccount = ref<BeneficiaryAccount>({
  bankCode: "",
  bankName: "",
  accountNumber: "",
  accountName: "",
  isActive: true,
  isDefault: false,
});

const newCategory = ref<PaymentCategory>({
  name: "",
  icon: "",
  color: "",
  isActive: true,
  expanded: true,
  methods: [],
});

// Bank mapping
const bankNames: Record<string, string> = {
  VCB: "Vietcombank",
  TCB: "Techcombank",
  VTB: "Vietinbank",
  BIDV: "BIDV",
  ACB: "ACB",
  MB: "MB Bank",
  TPB: "TPBank",
  SHB: "SHB",
};

// Methods
const getPaymentMethodColor = (type: string): string => {
  const colors: Record<string, string> = {
    bank: "bg-blue-600",
    momo: "bg-pink-600",
    zalopay: "bg-blue-500",
    vnpay: "bg-red-600",
    cash: "bg-green-600",
  };
  return colors[type] || "bg-gray-600";
};

const getPaymentMethodLabel = (type: string): string => {
  const labels: Record<string, string> = {
    bank: "BANK",
    momo: "MOMO",
    zalopay: "ZALO",
    vnpay: "VNPAY",
    cash: "CASH",
  };
  return labels[type] || "OTHER";
};

const maskAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 4) return accountNumber;
  const visiblePart = accountNumber.slice(-4);
  const maskedPart = "*".repeat(accountNumber.length - 4);
  return maskedPart + visiblePart;
};

const updateBankName = () => {
  newBeneficiaryAccount.value.bankName =
    bankNames[newBeneficiaryAccount.value.bankCode] || "";
};

const addPaymentMethod = () => {
  if (newPaymentMethod.value.isDefault) {
    paymentMethod.value.forEach((method) => (method.isDefault = false));
  }

  paymentMethod.value.push({ ...newPaymentMethod.value });

  // Reset form
  newPaymentMethod.value = {
    type: "",
    name: "",
    description: "",
    accountNumber: "",
    isActive: true,
    isDefault: false,
  };

  showAddPaymentModal.value = false;
};

const addBeneficiaryAccount = () => {
  if (newBeneficiaryAccount.value.isDefault) {
    beneficiaryAccounts.value.forEach((account) => (account.isDefault = false));
  }

  beneficiaryAccounts.value.push({ ...newBeneficiaryAccount.value });

  // Reset form
  newBeneficiaryAccount.value = {
    bankCode: "",
    bankName: "",
    accountNumber: "",
    accountName: "",
    isActive: true,
    isDefault: false,
  };

  showAddBeneficiaryModal.value = false;
};

const editPaymentMethod = (method: PaymentMethod) => {
  // TODO: Implement edit functionality
  console.log("Edit payment method:", method);
};

const deletePaymentMethod = (index: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa phương thức thanh toán này?")) {
    paymentMethod.value.splice(index, 1);
  }
};

const editBeneficiaryAccount = (account: BeneficiaryAccount) => {
  // TODO: Implement edit functionality
  console.log("Edit beneficiary account:", account);
};

const deleteBeneficiaryAccount = (index: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa tài khoản thụ hưởng này?")) {
    beneficiaryAccounts.value.splice(index, 1);
  }
};

// Tree Methods
const expandAllNodes = () => {
  paymentTree.value.forEach((category) => {
    category.expanded = true;
  });
};

const collapseAllNodes = () => {
  paymentTree.value.forEach((category) => {
    category.expanded = false;
  });
};

const toggleCategory = (categoryIndex: number) => {
  paymentTree.value[categoryIndex].expanded =
    !paymentTree.value[categoryIndex].expanded;
};

const toggleCategoryStatus = (categoryIndex: number) => {
  paymentTree.value[categoryIndex].isActive =
    !paymentTree.value[categoryIndex].isActive;
};

const toggleMethodStatus = (categoryIndex: number, methodIndex: number) => {
  paymentTree.value[categoryIndex].methods[methodIndex].isActive =
    !paymentTree.value[categoryIndex].methods[methodIndex].isActive;
};

// New toggle handlers for ToggleSwitch component
const handleCategoryToggle = (categoryIndex: number, value: boolean) => {
  paymentTree.value[categoryIndex].isActive = value;
  console.log(
    `Category ${paymentTree.value[categoryIndex].name} toggled to:`,
    value
  );
};

const handleMethodToggle = (
  categoryIndex: number,
  methodIndex: number,
  value: boolean
) => {
  paymentTree.value[categoryIndex].methods[methodIndex].isActive = value;
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log(`Method ${method.name} toggled to:`, value);
};

const editTreeMethod = (categoryIndex: number, methodIndex: number) => {
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log("Edit tree method:", method);
  // TODO: Implement edit functionality
};

const deleteTreeMethod = (categoryIndex: number, methodIndex: number) => {
  if (confirm("Bạn có chắc chắn muốn xóa phương thức này?")) {
    paymentTree.value[categoryIndex].methods.splice(methodIndex, 1);
  }
};

const showAddMethodToCategory = (categoryIndex: number) => {
  console.log("Add method to category:", paymentTree.value[categoryIndex].name);
  // TODO: Implement add method to category functionality
};

const addCategory = () => {
  paymentTree.value.push({ ...newCategory.value });

  // Reset form
  newCategory.value = {
    name: "",
    icon: "",
    color: "",
    isActive: true,
    expanded: true,
    methods: [],
  };

  showAddCategoryModal.value = false;
};

// API Payment Methods handlers
const toggleApiMethod = (methodId: string) => {
  const index = expandedApiMethods.value.indexOf(methodId);
  if (index > -1) {
    expandedApiMethods.value.splice(index, 1);
  } else {
    expandedApiMethods.value.push(methodId);
  }
};

const handleApiMethodToggle = (method: ApiPaymentMethod, value: boolean) => {
  console.log(`API Method ${method.name} toggled to:`, value);
  // TODO: Implement API method toggle functionality
};

const handleTitleToggle = (
  methodId: string,
  titleId: string,
  value: boolean
) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  if (title) {
    title.isActive = value;
    console.log(`Title ${title.name} toggled to:`, value);
  }
  // TODO: Implement title toggle functionality
};

const editApiTitle = (methodId: string, titleId: string) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  console.log("Edit API title:", title);
  // TODO: Implement edit functionality
};

// Helper functions for displaying title properties
const getDisplayableProperties = (title: BeneficiaryTitle) => {
  const excludeKeys = ["id", "name", "isDefault"]; // Keys to exclude from display
  const result: Record<string, any> = {};

  Object.entries(title).forEach(([key, value]) => {
    if (
      !excludeKeys.includes(key) &&
      value !== null &&
      value !== undefined &&
      value !== ""
    ) {
      result[key] = value;
    }
  });

  return result;
};

const formatPropertyName = (key: string): string => {
  const nameMap: Record<string, string> = {
    accountNumber: "Số TK",
    bankName: "Ngân hàng",
    bankCode: "Mã NH",
    isActive: "Trạng thái",
    accountName: "Tên TK",
    balance: "Số dư",
    currency: "Tiền tệ",
    type: "Loại",
    status: "Tình trạng",
    createdAt: "Ngày tạo",
    updatedAt: "Cập nhật",
  };

  return nameMap[key] || key.replace(/([A-Z])/g, " $1").trim();
};

const formatPropertyValue = (value: any): string => {
  if (typeof value === "boolean") {
    return value ? "Có" : "Không";
  }

  if (typeof value === "number") {
    return value.toLocaleString("vi-VN");
  }

  if (typeof value === "string" && value.includes("T") && value.includes("Z")) {
    // Likely a date string
    try {
      return new Date(value).toLocaleDateString("vi-VN");
    } catch {
      return value;
    }
  }

  return String(value);
};

//
const {
  paymentMethods,
  handleCreateGatewayConfig,
  handleUpdateGatewayConfig,
  getPaymentMethodTitles,
} = usePayment();
const handleGetPaymentMethods = async () => {
  try {
    isLoadingPaymentMethods.value = true;
    const response = await paymentMethods();
    console.log("Payment methods response:", response);

    // Store the API response data
    apiPaymentMethods.value = response || [];

    // For each payment method, fetch titles if needed
    for (const method of apiPaymentMethods.value) {
      if (method.code && !method.titles) {
        try {
          const titlesResponse = await getPaymentMethodTitles(method.code);
          method.titles = titlesResponse || [];
        } catch (error) {
          console.warn(`Failed to fetch titles for ${method.code}:`, error);
          method.titles = [];
        }
      }
    }

    isLoadingPaymentMethods.value = false;
  } catch (error) {
    isLoadingPaymentMethods.value = false;
    console.error("Error fetching payment methods:", error);
    throw error;
  }
};
const createGatewayConfig = async (
  methodCode: String,
  subMethodCode: string,
  gwPartnerCode: string
) => {
  try {
    const response = await handleCreateGatewayConfig(
      methodCode,
      subMethodCode,
      gwPartnerCode,
      ""
    );
    console.log("response", response);
  } catch (error) {
    throw error;
  }
};
const updateGatewayConfig = async (
  methodCode: String,
  subMethodCode: string,
  gwPartnerCode: string,
  gatewayId: string,
  cassoApiKey: string
) => {
  try {
    const response = await handleUpdateGatewayConfig(
      methodCode,
      subMethodCode,
      gwPartnerCode,
      gatewayId,
      cassoApiKey
    );
    console.log("responses", response);
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetPaymentMethods();
});
</script>
