<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h2 class="text-lg font-medium text-gray-900">
        <PERSON><PERSON><PERSON> <PERSON>ì<PERSON> phương thức thanh toán
      </h2>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> lý các ph<PERSON><PERSON><PERSON> thức thanh toán và tài khoản thụ hưởng
      </p>
    </div>
    <!-- Payment Method List (Toggle Style) -->
    <div class="bg-white rounded border border-gray-200">
      <div
        class="flex items-center justify-between p-4 border-b border-gray-200"
      >
        <h3 class="text-base font-medium text-gray-900">
          Danh sách phương thức thanh toán
        </h3>
        <div class="flex items-center gap-3">
          <button
            @click="handleGetPaymentMethods"
            :disabled="isLoadingPaymentMethods"
            class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1 disabled:opacity-50"
          >
            <svg
              class="w-4 h-4"
              :class="{ 'animate-spin': isLoadingPaymentMethods }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            {{ isLoadingPaymentMethods ? "Đang tải..." : "Làm mới API" }}
          </button>
        </div>
      </div>

      <!-- Payment Methods List -->
      <div class="divide-y divide-gray-200">
        <!-- API Payment Methods -->
        <ApiPaymentMethodItem
          v-for="method in apiPaymentMethods"
          :key="method.id"
          :method="method"
          :is-expanded="expandedApiMethods.includes(method.id)"
          @toggle-expanded="toggleApiMethod(method.id)"
          @method-toggle="handleApiMethodToggle(method, $event)"
          @title-toggle="
            (titleId, value) => handleTitleToggle(method.id, titleId, value)
          "
          @title-edit="editApiTitle(method.id, $event)"
        />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Hủy
      </button>
      <button
        type="button"
        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Lưu thay đổi
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface BeneficiaryAccount {
  bankCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  isActive: boolean;
  isDefault: boolean;
}

interface TreeMethod {
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  details?: Record<string, string>;
}

interface PaymentCategory {
  name: string;
  icon: string;
  color: string;
  isActive: boolean;
  expanded: boolean;
  methods: TreeMethod[];
  hasDefault?: boolean;
}

// Reactive data
const showAddBeneficiaryModal = ref(false);
const showAddCategoryModal = ref(false);

// API Payment Methods data
const apiPaymentMethods = ref<ApiPaymentMethod[]>([]);
const isLoadingPaymentMethods = ref(false);
const expandedApiMethods = ref<string[]>([]);

const beneficiaryAccounts = ref<BeneficiaryAccount[]>([
  {
    bankCode: "CTG",
    bankName: "Viettinbank",
    accountNumber: "************",
    accountName: "DO NGOC DUY HUNG",
    isActive: true,
    isDefault: true,
  },
]);

// Payment Tree Structure
const paymentTree = ref<PaymentCategory[]>([
  {
    name: "Tiền mặt",
    icon: "💵",
    color: "bg-green-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
  {
    name: "Chuyển khoản",
    icon: "🏦",
    color: "bg-blue-600",
    isActive: true,
    expanded: true,
    hasDefault: true,
    methods: [
      {
        name: "Do Ngoc Duy Hung",
        description: "VietinBank - Ngân hàng TMCP Công thương Việt Nam",
        isActive: true,
        isDefault: true,
      },
    ],
  },
  {
    name: "Thanh toán thẻ",
    icon: "💳",
    color: "bg-purple-600",
    isActive: true,
    expanded: false,
    hasDefault: false,
    methods: [],
  },
]);

const newBeneficiaryAccount = ref<BeneficiaryAccount>({
  bankCode: "",
  bankName: "",
  accountNumber: "",
  accountName: "",
  isActive: true,
  isDefault: false,
});

const newCategory = ref<PaymentCategory>({
  name: "",
  icon: "",
  color: "",
  isActive: true,
  expanded: true,
  methods: [],
});

// Bank mapping
const bankNames: Record<string, string> = {
  VCB: "Vietcombank",
  TCB: "Techcombank",
  VTB: "Vietinbank",
  BIDV: "BIDV",
  ACB: "ACB",
  MB: "MB Bank",
  TPB: "TPBank",
  SHB: "SHB",
};

// Methods
const toggleCategory = (categoryIndex: number) => {
  paymentTree.value[categoryIndex].expanded =
    !paymentTree.value[categoryIndex].expanded;
};

// New toggle handlers for ToggleSwitch component
const handleCategoryToggle = (categoryIndex: number, value: boolean) => {
  paymentTree.value[categoryIndex].isActive = value;
  console.log(
    `Category ${paymentTree.value[categoryIndex].name} toggled to:`,
    value
  );
};

const handleMethodToggle = (
  categoryIndex: number,
  methodIndex: number,
  value: boolean
) => {
  paymentTree.value[categoryIndex].methods[methodIndex].isActive = value;
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log(`Method ${method.name} toggled to:`, value);
};

const editTreeMethod = (categoryIndex: number, methodIndex: number) => {
  const method = paymentTree.value[categoryIndex].methods[methodIndex];
  console.log("Edit tree method:", method);
  // TODO: Implement edit functionality
};

const showAddMethodToCategory = (categoryIndex: number) => {
  console.log("Add method to category:", paymentTree.value[categoryIndex].name);
  // TODO: Implement add method to category functionality
};

// API Payment Methods handlers
const toggleApiMethod = (methodId: string) => {
  const index = expandedApiMethods.value.indexOf(methodId);
  if (index > -1) {
    expandedApiMethods.value.splice(index, 1);
  } else {
    expandedApiMethods.value.push(methodId);
  }
};

const handleApiMethodToggle = (method: ApiPaymentMethod, value: boolean) => {
  console.log(`API Method ${method.name} toggled to:`, value);
  // TODO: Implement API method toggle functionality
};

const handleTitleToggle = (
  methodId: string,
  titleId: string,
  value: boolean
) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  if (title) {
    title.isActive = value;
    console.log(`Title ${title.name} toggled to:`, value);
  }
  // TODO: Implement title toggle functionality
};

const editApiTitle = (methodId: string, titleId: string) => {
  const method = apiPaymentMethods.value.find((m) => m.id === methodId);
  const title = method?.titles?.find((t) => t.id === titleId);
  console.log("Edit API title:", title);
  // TODO: Implement edit functionality
};

//
const { paymentMethods, getPaymentMethodTitles } = usePayment();
const handleGetPaymentMethods = async () => {
  try {
    isLoadingPaymentMethods.value = true;
    const response = await paymentMethods();
    console.log("Payment methods response:", response);

    // Store the API response data
    apiPaymentMethods.value = response || [];

    // For each payment method, fetch titles if needed
    for (const method of apiPaymentMethods.value) {
      if (method.code && !method.titles) {
        try {
          const titlesResponse = await getPaymentMethodTitles(method.code);
          method.titles = titlesResponse || [];
        } catch (error) {
          console.warn(`Failed to fetch titles for ${method.code}:`, error);
          method.titles = [];
        }
      }
    }

    isLoadingPaymentMethods.value = false;
  } catch (error) {
    isLoadingPaymentMethods.value = false;
    console.error("Error fetching payment methods:", error);
    throw error;
  }
};

onMounted(async () => {
  await handleGetPaymentMethods();
});
</script>
