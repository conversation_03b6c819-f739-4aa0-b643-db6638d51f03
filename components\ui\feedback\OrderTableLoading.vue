<template>
  <tbody>
    <tr
      v-for="n in itemCount"
      :key="n"
      class="even:bg-gray-50 odd:bg-white animate-pulse"
    >
      <!-- M<PERSON> đơn -->
      <td class="p-2 text-center">
        <div class="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
      </td>

      <!-- Khách hàng -->
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-24"></div>
          <div class="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </td>
      <!-- Nhân viên -->
      <td class="p-2">
        <div class="h-4 bg-gray-200 rounded w-20"></div>
      </td>

      <!-- Sản phẩm -->
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-3 bg-gray-200 rounded w-full"></div>
          <div class="h-3 bg-gray-200 rounded w-3/4"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </td>

      <!-- Tổng giảm -->
      <td class="p-2">
        <div class="h-4 bg-gray-200 rounded w-16"></div>
      </td>

      <!-- Thanh toán -->
      <td class="p-2">
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-20"></div>
          <div class="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </td>

      <!-- Actions -->
      <td class="p-2 text-center">
        <div class="h-6 w-6 bg-gray-200 rounded mx-auto"></div>
      </td>
    </tr>
  </tbody>
</template>

<script setup lang="ts">
interface Props {
  itemCount?: number;
}

withDefaults(defineProps<Props>(), {
  itemCount: 5,
});
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
