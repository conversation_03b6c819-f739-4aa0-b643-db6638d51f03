<template>
  <div class="bg-white border-b border-gray-200 px-4 py-3">
    <!-- Mobile Layout -->
    <div class="md:hidden space-y-3">
      <!-- Search -->
      <div class="relative">
        <input
          type="text"
          v-model="filters.search"
          @input="$emit('filter-change', filters)"
          placeholder="Tìm kiếm giao dịch..."
          class="pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm outline-none w-full"
        />
        <svg
          class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>

      <!-- Filters Grid -->
      <div class="flex gap-3">
        <!-- Date Picker Mobile -->
        <div class="flex-1">
          <flat-pickr
            class="w-full py-3 px-3 text-sm border border-gray-300 rounded-lg outline-none"
            placeholder="Chọn khoảng thời gian"
            :config="datePickerConfig"
            v-model="dateRange"
          />
        </div>

        <!-- Clear Filter Button Mobile -->
        <button
          @click="clearFilters"
          :disabled="!hasActiveFilters"
          class="px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 whitespace-nowrap"
          :class="
            hasActiveFilters
              ? 'bg-red-50 text-red-600 border border-red-200 hover:bg-red-100'
              : 'bg-gray-50 text-gray-400 border border-gray-200 cursor-not-allowed'
          "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          <span class="hidden sm:inline">Xóa bộ lọc</span>
          <span class="sm:hidden">Xóa</span>
        </button>
      </div>
    </div>

    <!-- Desktop Layout -->
    <div class="hidden md:flex items-center gap-4 flex-wrap">
      <!-- Search -->
      <div class="flex items-center gap-2 w-full">
        <div class="w-full relative">
          <input
            type="text"
            v-model="filters.search"
            @input="$emit('filter-change', filters)"
            placeholder="Tìm kiếm giao dịch..."
            class="p-2 border border-gray-300 pl-10 rounded-lg text-sm outline-none w-full"
          />
          <svg
            class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <flat-pickr
          class="col-span-1 min-w-[250px] py-[6px] px-2 text-base border border-gray-300 rounded-lg outline-none text-center"
          placeholder="Chọn khoảng thời gian"
          :config="datePickerConfig"
          v-model="dateRange"
        />

        <!-- Clear Filter Button Desktop -->
        <button
          @click="clearFilters"
          :disabled="!hasActiveFilters"
          class="px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 flex items-center gap-2 whitespace-nowrap "
          :class="
            hasActiveFilters
              ? 'bg-red-50 text-red-600 border border-red-200 hover:bg-red-100'
              : 'bg-gray-50 text-gray-400 border border-gray-200 cursor-not-allowed'
          "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          Xóa bộ lọc
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
});
const dateRange = ref([]);
const emit = defineEmits(["filter-change"]);

const datePickerConfig = ref({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: (selectedDates) => {
    if (selectedDates.length === 2) {
      filters.dateRange = selectedDates;
      emit("filter-change", filters);
    }
  },
});

const filters = reactive({
  dateRange: "",
  search: "",
});

// Computed property to check if any filters are active
const hasActiveFilters = computed(() => {
  return (
    filters.search.trim() !== "" ||
    (dateRange.value && dateRange.value.length > 0)
  );
});

// Clear all filters
const clearFilters = () => {
  filters.search = "";
  filters.dateRange = "";
  dateRange.value = [];

  // Emit the cleared filters
  emit("filter-change", filters);
};
</script>
