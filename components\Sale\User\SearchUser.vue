<template>
  <div class="search-user-container w-full m-auto p-1">
    <div v-if="isLoading" class="flex items-center justify-center py-4">
      <Suspense>
        <LoadingSpinner />
        <template #fallback>
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
        </template>
      </Suspense>
    </div>

    <div v-else>
      <header class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-1">
          <span class="text-primary font-semibold">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
              />
            </svg>
          </span>
          <h3 class="text-sm font-semibold text-primary">Khách hàng</h3>
        </div>
        <button
          @click="handleCreateCustommer"
          class="p-1 text-primary hover:text-primary-dark hover:bg-primary/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 transition-all duration-200 group"
          aria-label="Tạo khách hàng mới"
          type="button"
        >
          <svg
            class="w-5 h-5 group-hover:scale-110 transition-transform duration-200"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"
            />
          </svg>
        </button>
      </header>

      <div
        v-if="users.length === 0 && isAlert && searchPhone"
        class="flex items-center gap-2 p-2 mb-2 text-xs text-red-700 bg-red-50 border border-red-200 rounded-lg"
        role="alert"
      >
        <svg
          class="w-4 h-4 flex-shrink-0"
          fill="currentColor"
          viewBox="0 0 20 20"
          aria-hidden="true"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
            clip-rule="evenodd"
          />
        </svg>
        <span>Không có thông tin khách hàng vui lòng tạo mới</span>
      </div>
      <!-- ✅ Enhanced Search Input Section -->
      <div class="relative">
        <div class="grid grid-cols-2 gap-2">
          <div class="relative col-span-2 mb-2">
            <!-- ✅ Enhanced Search Input with better styling -->
            <div class="relative">
              <input
                v-model="searchPhone"
                @input="debounceSearch('phone')"
                type="tel"
                placeholder="Số điện thoại"
                class="w-full py-1 px-2 pr-8 text-sm md:text-sm bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 placeholder-gray-400"
                autocomplete="tel"
                aria-label="Tìm kiếm khách hàng theo số điện thoại"
              />

              <!-- ✅ Enhanced Loading Indicator -->
              <div
                v-if="isLoadingPhone && searchPhone"
                class="absolute inset-y-0 right-0 flex items-center pr-2"
              >
                <div
                  class="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"
                ></div>
              </div>

              <!-- ✅ Enhanced Clear Button -->
              <button
                v-if="!isLoadingPhone && searchPhone"
                @click="handleClearSearch"
                class="absolute inset-y-0 right-0 flex items-center pr-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200"
                aria-label="Xóa tìm kiếm"
                type="button"
              >
                <svg
                  class="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- ✅ Enhanced Search Results Dropdown -->
        <div
          v-if="shouldShowResults"
          class="absolute z-20 w-full mt-1 bg-white rounded-lg shadow-xl border border-gray-200 max-h-64 overflow-y-auto"
          style="scrollbar-width: thin; scrollbar-color: #d1d5db #f9fafb"
        >
          <div
            v-for="user in users"
            :key="user.id"
            @click="handleAddCustomer(user)"
            class="group p-3 flex items-center cursor-pointer hover:bg-gray-50 focus:bg-gray-50 transition-all duration-200 border-b border-gray-100 last:border-b-0"
            role="button"
            tabindex="0"
            @keydown.enter="handleAddCustomer(user)"
            @keydown.space.prevent="handleAddCustomer(user)"
          >
            <!-- ✅ Enhanced User Avatar -->
            <div class="flex-shrink-0 mr-3">
              <div
                class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center text-white font-semibold text-sm shadow-sm transition-shadow duration-200"
              >
                {{ user.name.charAt(0).toUpperCase() }}
              </div>
            </div>

            <!-- ✅ Enhanced User Information -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2">
                <span
                  class="block text-sm font-medium text-gray-900 capitalize truncate group-hover:text-primary transition-colors duration-200"
                >
                  {{ user.name }}
                </span>
                <span
                  v-if="user.isVip"
                  class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                >
                  VIP
                </span>
              </div>
              <div class="flex items-center gap-2 mt-1">
                <svg
                  class="w-3 h-3 text-gray-400 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="text-xs text-gray-500 font-mono">{{
                  user.phone
                }}</span>
              </div>
            </div>

            <!-- ✅ Selection Indicator -->
            <div
              class="flex-shrink-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            >
              <svg
                class="w-4 h-4 text-primary"
                fill="currentColor"
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ConfirmDialog
      v-if="isEditOrder"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh khách hàng`"
      @confirm="confirm"
      @cancel="cancel"
    ></ConfirmDialog>
    <ModalCreateCustomer
      v-if="isModalCreateCustomer"
      @close="toggleModalCreateCustomer"
      @closeModelUser="closeModelUser"
      @dataCreateCustomer="dataCreateCustomer"
      :phoneCusTomer="searchPhone"
    ></ModalCreateCustomer>
  </div>
</template>

<script setup lang="ts">
// ✅ Enhanced TypeScript Interfaces
interface User {
  id: string;
  name: string;
  phone: string;
  email?: string;
  isVip?: boolean;
}

interface SearchRequest {
  keyword: string;
  currentPage: number;
  pageSize: number;
}

interface SearchResponse {
  content: User[];
  totalElements?: number;
}

// ✅ Lazy Loading Components with Enhanced Error Handling
const LoadingSpinner = defineAsyncComponent({
  loader: () => import("~/components/common/LoadingSpinner.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", {
      class: "animate-spin rounded-full h-6 w-6 border-b-2 border-primary",
    }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-2" }, "Failed to load spinner"),
});

const ConfirmDialog = defineAsyncComponent({
  loader: () => import("~/components/dialog/ConfirmDialog.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-20 rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-4" }, "Failed to load dialog"),
});

const ModalCreateCustomer = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalCreateCustomer.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-40 rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-4" }, "Failed to load modal"),
});

// ✅ Reactive State with TypeScript
const searchQuery = ref<string>("");
const searchPhone = ref<string>("");
const users = ref<User[]>([]);
const isDivVisible = ref<boolean>(true);
const timeoutId = ref<ReturnType<typeof setTimeout> | null>(null);
const isLoadingName = ref<boolean>(false);
const isLoadingPhone = ref<boolean>(false);
const cache = ref<{ [key: string]: User[] }>({});
const isAlert = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const isEditOrder = ref<boolean>(false);
const userDraft = ref<User | null>(null);
const isModalCreateCustomer = ref<boolean>(false);

// ✅ Composables
const { fetchListCustomer } = useCustomer();
const { addCustomerToOrder } = useOrderStore();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);

// ✅ Enhanced Computed Properties
const shouldShowResults = computed(() => {
  return (
    (searchQuery.value || searchPhone.value) &&
    isDivVisible.value &&
    users.value.length > 0
  );
});

// ✅ Enhanced Debounced Search Function
const debounceSearch = (type: string) => {
  if (timeoutId.value) clearTimeout(timeoutId.value);
  timeoutId.value = setTimeout(async () => {
    await searchCustomers(type);
    isAlert.value = true;
  }, 500);
};

// ✅ Enhanced Search Function with Error Handling
const searchCustomers = async (type: string) => {
  const searchTerm = type === "name" ? searchQuery.value : searchPhone.value;

  if (type === "name") {
    if (!searchQuery.value) {
      isLoadingName.value = false;
      return;
    }
    isLoadingName.value = true;
  } else if (type === "phone") {
    if (!searchPhone.value) {
      isLoadingPhone.value = false;
      return;
    }
    isLoadingPhone.value = true;
  }

  // Check cache first
  if (cache.value[searchTerm]) {
    users.value = cache.value[searchTerm];
    isDivVisible.value = true;
    if (type === "name") {
      isLoadingName.value = false;
    } else if (type === "phone") {
      isLoadingPhone.value = false;
    }
    return;
  }

  try {
    const request: SearchRequest = {
      keyword: searchTerm,
      currentPage: 1,
      pageSize: 20,
    };

    const response: SearchResponse = await fetchListCustomer(request);
    users.value = response.content;
    cache.value[searchTerm] = response.content;
    isDivVisible.value = true;
  } catch (error) {
    console.error("Error searching customers:", error);
    users.value = [];
    useNuxtApp().$toast.error("Có lỗi xảy ra khi tìm kiếm khách hàng");
  } finally {
    if (type === "name") {
      isLoadingName.value = false;
    } else if (type === "phone") {
      isLoadingPhone.value = false;
    }
  }
};

// ✅ Enhanced Modal Management Functions
const toggleModalCreateCustomer = () => {
  isModalCreateCustomer.value = !isModalCreateCustomer.value;
};

const handleCreateCustommer = () => {
  isModalCreateCustomer.value = !isModalCreateCustomer.value;
};

const handleClearSearch = () => {
  searchPhone.value = "";
  users.value = [];
  isDivVisible.value = false;
  isAlert.value = false;
};

const closeModelUser = (value: boolean) => {
  isModalCreateCustomer.value = value;
};

const dataCreateCustomer = (response: User) => {
  try {
    addCustomerToOrder(response, "");
    useNuxtApp().$toast.success("Tạo khách hàng thành công");
  } catch (error) {
    console.error("Error creating customer:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi tạo khách hàng");
  }
};

// ✅ Enhanced Customer Selection Functions
const handleAddCustomer = async (user: User) => {
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrder.value = true;
    userDraft.value = user;
  } else {
    try {
      isLoading.value = true;
      await addCustomerToOrder(user, "");
      users.value = [];
      searchPhone.value = "";
      isDivVisible.value = false;
      useNuxtApp().$toast.success("Thêm khách hàng thành công");
    } catch (error) {
      console.error("Error adding customer:", error);
      useNuxtApp().$toast.error("Có lỗi xảy ra khi thêm khách hàng");
    } finally {
      isLoading.value = false;
    }
  }
};

const cancel = () => {
  isEditOrder.value = false;
  users.value = [];
  searchPhone.value = "";
  isDivVisible.value = false;
  userDraft.value = null;
};

const confirm = async () => {
  try {
    isLoading.value = true;
    await addCustomerToOrder(userDraft.value, "");
    users.value = [];
    searchPhone.value = "";
    isDivVisible.value = false;
    useNuxtApp().$toast.success("Cập nhật khách hàng thành công");
  } catch (error) {
    console.error("Error confirming customer:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi cập nhật khách hàng");
  } finally {
    isLoading.value = false;
    isEditOrder.value = false;
    userDraft.value = null;
  }
};
</script>

<style scoped>
/* ✅ Enhanced Component Styles with Tailwind CSS */

/* Custom animations for smooth interactions */
.search-user-container {
  transition: all 0.2s ease-in-out;
}

/* Custom focus styles for better accessibility */
input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: transparent;
}

button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px currentColor;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Custom scrollbar for dropdown */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Enhanced loading animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient avatar animation */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Group hover effects */
.group:hover .group-hover\:shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.group:hover .group-hover\:text-primary {
  color: rgb(var(--color-primary));
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .search-user-container {
    margin: 0.25rem;
  }

  .dropdown-item {
    padding: 0.75rem;
  }

  .user-avatar {
    width: 2rem;
    height: 2rem;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .search-user-container {
    background-color: rgb(31 41 55);
    color: rgb(243 244 246);
  }

  input {
    background-color: rgb(55 65 81);
    border-color: rgb(75 85 99);
    color: rgb(243 244 246);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .search-user-container {
    border: 2px solid black;
  }

  button {
    border: 1px solid currentColor;
  }

  input {
    border: 2px solid black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .animate-pulse,
  .animate-spin {
    animation: none;
    transition: none;
  }

  .group-hover\:scale-110 {
    transform: none;
  }
}

/* Focus trap for dropdown */
.dropdown-container {
  position: relative;
}

.dropdown-container:focus-within .dropdown-menu {
  display: block;
}

/* Loading state improvements */
.loading-overlay {
  backdrop-filter: blur(2px);
}

/* Alert message enhancements */
.alert-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
