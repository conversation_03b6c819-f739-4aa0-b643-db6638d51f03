<template>
  <div class="bg-white h-full flex flex-col">
    <!-- Desktop Table Layout -->
    <div class="hidden md:flex flex-col h-full">
      <!-- Table Header - Fixed -->
      <div class="border-b border-gray-200 flex-shrink-0 bg-blue-50">
        <div
          class="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-gray-700"
        >
          <div class="col-span-4">Sản phẩm</div>
          <div class="col-span-2">Danh mục</div>
          <div class="col-span-2 text-center">G<PERSON><PERSON> b<PERSON></div>
          <div class="col-span-2 text-center">Giá <PERSON>M</div>
          <div class="col-span-1">Đơn vị</div>
          <div class="col-span-1">Thao tác</div>
        </div>
      </div>

      <!-- Table Body - Scrollable Content -->
      <div class="flex-1 overflow-y-auto min-h-0">
        <div
          v-if="ListProduct?.length || isLoading"
          class="divide-y divide-gray-200"
        >
          <!-- Loading Skeleton -->
          <ProductTableLoading
            v-if="isLoading"
            :item-count="itemsPerPage"
            :show-mobile="false"
          />

          <!-- Product Rows -->
          <div
            v-else
            v-for="product in ListProduct"
            :key="product?.id + '-' + product?.updatedAt"
            class="grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-50 transition-colors"
          >
            <ItemManagerTableProduct
              :product="product"
              :dataUnit="dataUnit"
              :dataCategories="dataCategories"
              @updateProduct="handleUpdateProduct"
              @viewDetail="handleViewDetail"
            />
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-else
          class="flex flex-col items-center justify-center py-16 text-center h-full"
        >
          <svg
            class="w-16 h-16 text-gray-300 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Chưa có sản phẩm nào
          </h3>
          <p class="text-gray-500">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
        </div>
      </div>

      <!-- Desktop Pagination - Fixed at bottom -->
      <UiPagination
        v-if="ListProduct.length > 0 || isLoading"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        :items-per-page="itemsPerPage"
        item-label="sản phẩm"
        @page-change="$emit('page-change', $event)"
      />
    </div>

    <!-- Mobile Card Layout -->
    <div class="md:hidden h-full flex flex-col">
      <!-- Mobile Content - Scrollable -->
      <div class="flex-1 overflow-y-auto min-h-0">
        <div v-if="ListProduct?.length || isLoading" class="p-4 space-y-3">
          <!-- Mobile Loading Skeleton -->
          <ProductTableLoading
            v-if="isLoading"
            :item-count="itemsPerPage"
            :show-desktop="false"
          />

          <!-- Mobile Product Cards -->
          <div v-else class="space-y-3">
            <div
              v-for="product in ListProduct"
              :key="product?.id"
              class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <ItemProductMobile
                :product="product"
                :dataUnit="dataUnit"
                @viewDetail="handleViewDetail"
              />
            </div>
          </div>
        </div>

        <!-- Mobile Empty State -->
        <div
          v-else
          class="flex flex-col items-center justify-center py-16 text-center h-full"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-16 w-16 text-gray-300 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
          <p class="text-lg font-medium text-gray-900 mb-2">
            Không tìm thấy sản phẩm
          </p>
          <p class="text-sm text-gray-500">
            Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
          </p>
        </div>
      </div>

      <!-- Mobile Pagination - Fixed at bottom -->
      <UiPagination
        v-if="ListProduct.length > 0 || isLoading"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        :items-per-page="itemsPerPage"
        item-label="sản phẩm"
        :show-items-info="false"
        min-height="70px"
        @page-change="$emit('page-change', $event)"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
// Import UI components
import UiPagination from "~/components/ui/navigation/Pagination.vue";
import ProductTableLoading from "~/components/ui/feedback/ProductTableLoading.vue";

interface Props {
  ListProduct: any[];
  isLoading: boolean;
  dataUnit: any[];
  dataCategories: any[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

const {
  ListProduct,
  isLoading,
  dataUnit,
  dataCategories,
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
} = defineProps<Props>();
const emit = defineEmits(["updateProduct", "viewDetail", "page-change"]);

const handleUpdateProduct = (productId: string) => {
  emit("updateProduct", productId);
};

const handleViewDetail = (productId: string) => {
  emit("viewDetail", productId);
};
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
</style>
