/**
 * Optimized Navigation Composable
 * Reduces router navigation delays by preloading and caching
 */

interface NavigationCache {
  [key: string]: {
    preloaded: boolean;
    timestamp: number;
  }
}

const navigationCache: NavigationCache = {};
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

export const useOptimizedNavigation = () => {
  const router = useRouter();
  const route = useRoute();

  /**
   * Preload a route component
   */
  const preloadRoute = async (path: string) => {
    try {
      const cacheKey = path;
      const now = Date.now();
      
      // Check if already preloaded and still valid
      if (navigationCache[cacheKey] && 
          (now - navigationCache[cacheKey].timestamp) < CACHE_DURATION) {
        return;
      }

      // Preload the route
      await router.resolve(path);
      
      // Cache the preload
      navigationCache[cacheKey] = {
        preloaded: true,
        timestamp: now
      };
      
      console.log(`[Navigation] Preloaded route: ${path}`);
    } catch (error) {
      console.warn(`[Navigation] Failed to preload route: ${path}`, error);
    }
  };

  /**
   * Fast navigation with preloading
   */
  const fastNavigateTo = async (path: string, options: any = {}) => {
    try {
      // Start navigation immediately
      const navigationPromise = navigateTo(path, options);
      
      // Preload related routes in background
      preloadRelatedRoutes(path);
      
      return await navigationPromise;
    } catch (error) {
      console.error('[Navigation] Fast navigation failed:', error);
      throw error;
    }
  };

  /**
   * Preload related routes based on current path
   */
  const preloadRelatedRoutes = (currentPath: string) => {
    const relatedRoutes: { [key: string]: string[] } = {
      '/sale': ['/diary', '/product', '/customer'],
      '/diary': ['/sale', '/diaries'],
      '/product': ['/sale', '/category'],
      '/customer': ['/sale', '/order'],
      '/dashboard': ['/feature', '/org'],
      '/feature': ['/sale', '/diary', '/product']
    };

    const related = relatedRoutes[currentPath] || [];
    related.forEach(route => {
      // Preload with small delay to not block current navigation
      setTimeout(() => preloadRoute(route), 100);
    });
  };

  /**
   * Optimized navigation with loading state
   */
  const navigateWithLoading = async (
    path: string, 
    options: any = {},
    showLoading = true
  ) => {
    const isLoading = ref(false);
    
    try {
      if (showLoading) {
        isLoading.value = true;
      }
      
      await fastNavigateTo(path, options);
    } finally {
      if (showLoading) {
        // Small delay to prevent flash
        setTimeout(() => {
          isLoading.value = false;
        }, 50);
      }
    }
    
    return { isLoading: readonly(isLoading) };
  };

  /**
   * Batch preload multiple routes
   */
  const batchPreload = (routes: string[]) => {
    routes.forEach((route, index) => {
      // Stagger preloading to avoid overwhelming
      setTimeout(() => preloadRoute(route), index * 50);
    });
  };

  /**
   * Clear navigation cache
   */
  const clearNavigationCache = () => {
    Object.keys(navigationCache).forEach(key => {
      delete navigationCache[key];
    });
    console.log('[Navigation] Cache cleared');
  };

  /**
   * Get cache statistics
   */
  const getCacheStats = () => {
    const now = Date.now();
    const validEntries = Object.entries(navigationCache).filter(
      ([_, cache]) => (now - cache.timestamp) < CACHE_DURATION
    );
    
    return {
      total: Object.keys(navigationCache).length,
      valid: validEntries.length,
      expired: Object.keys(navigationCache).length - validEntries.length
    };
  };

  // Auto-preload on mount
  onMounted(() => {
    // Preload common routes after initial load
    setTimeout(() => {
      const commonRoutes = ['/sale', '/diary', '/product', '/customer'];
      batchPreload(commonRoutes);
    }, 1000);
  });

  // Cleanup expired cache entries periodically
  if (process.client) {
    setInterval(() => {
      const now = Date.now();
      Object.keys(navigationCache).forEach(key => {
        if ((now - navigationCache[key].timestamp) > CACHE_DURATION) {
          delete navigationCache[key];
        }
      });
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  return {
    preloadRoute,
    fastNavigateTo,
    navigateWithLoading,
    batchPreload,
    clearNavigationCache,
    getCacheStats,
    preloadRelatedRoutes
  };
};
