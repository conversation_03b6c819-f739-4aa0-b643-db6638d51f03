<template>
  <label class="relative inline-flex items-center cursor-pointer">
    <input
      type="checkbox"
      :checked="modelValue"
      @change="toggle"
      class="sr-only peer"
      :disabled="disabled"
    />
    <div
      class="bg-gray-200 peer-focus:outline-none peer-focus:ring-4  rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:bg-white after:border-gray-300 after:border after:rounded-full after:transition-all peer-checked:bg-primary"
      :class="[
        sizeClasses,
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      ]"
    ></div>
  </label>
</template>

<script setup lang="ts">
// Props
interface Props {
  modelValue: boolean;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  disabled: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'change': [value: boolean];
}>();

// Size configurations
const sizeConfigs = {
  sm: {
    container: 'w-9 h-5',
    after: 'after:top-[1px] after:left-[1px] after:h-4 after:w-4',
    translate: 'peer-checked:after:translate-x-4'
  },
  md: {
    container: 'w-11 h-6',
    after: 'after:top-[2px] after:left-[2px] after:h-5 after:w-5',
    translate: 'peer-checked:after:translate-x-5'
  },
  lg: {
    container: 'w-14 h-7',
    after: 'after:top-[2px] after:left-[2px] after:h-6 after:w-6',
    translate: 'peer-checked:after:translate-x-7'
  }
};

// Computed classes
const sizeClasses = computed(() => {
  const config = sizeConfigs[props.size];
  return `${config.container} ${config.after} ${config.translate}`;
});

// Methods
const toggle = () => {
  if (props.disabled) return;
  
  const newValue = !props.modelValue;
  emit('update:modelValue', newValue);
  emit('change', newValue);
};
</script>
