/**
 * Optimized Async Component Factory
 * Reduces loading delays and improves performance
 */

interface OptimizedAsyncOptions {
  delay?: number;
  timeout?: number;
  priority?: 'high' | 'medium' | 'low';
  preload?: boolean;
  retries?: number;
}

/**
 * Create optimized async component with smart defaults
 */
export const createOptimizedAsyncComponent = (
  loader: () => Promise<any>,
  options: OptimizedAsyncOptions = {}
) => {
  const {
    delay = 0, // No delay for better UX
    timeout = 2000, // Shorter timeout
    priority = 'medium',
    preload = false,
    retries = 1
  } = options;

  // Adjust settings based on priority
  const prioritySettings = {
    high: { delay: 0, timeout: 1000 },
    medium: { delay: 50, timeout: 2000 },
    low: { delay: 100, timeout: 3000 }
  };

  const settings = prioritySettings[priority];

  return defineAsyncComponent({
    loader: async () => {
      let lastError;
      
      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          return await loader();
        } catch (error) {
          lastError = error;
          if (attempt < retries) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
          }
        }
      }
      
      throw lastError;
    },
    delay: settings.delay,
    timeout: settings.timeout,
    loadingComponent: createLoadingComponent(priority),
    errorComponent: createErrorComponent(),
  });
};

/**
 * Create lightweight loading component
 */
const createLoadingComponent = (priority: string) => {
  const loadingComponents = {
    high: () => h('div', { 
      class: 'animate-pulse bg-gray-100 rounded h-8 w-full' 
    }),
    medium: () => h('div', { 
      class: 'animate-pulse bg-gray-100 rounded h-12 w-full' 
    }),
    low: () => h('div', { 
      class: 'animate-pulse bg-gray-100 rounded h-16 w-full' 
    })
  };
  
  return loadingComponents[priority as keyof typeof loadingComponents] || loadingComponents.medium;
};

/**
 * Create lightweight error component
 */
const createErrorComponent = () => {
  return () => h('div', { 
    class: 'text-red-500 text-sm p-2 bg-red-50 rounded border border-red-200' 
  }, 'Component failed to load');
};

/**
 * Preload component for better performance
 */
export const preloadComponent = async (loader: () => Promise<any>) => {
  try {
    await loader();
  } catch (error) {
    console.warn('Failed to preload component:', error);
  }
};

/**
 * Batch preload multiple components
 */
export const batchPreloadComponents = (loaders: (() => Promise<any>)[]) => {
  loaders.forEach((loader, index) => {
    // Stagger preloading
    setTimeout(() => preloadComponent(loader), index * 50);
  });
};

/**
 * Common optimized async components
 */
export const OptimizedComponents = {
  // High priority - critical UI components
  LoadingSpinner: createOptimizedAsyncComponent(
    () => import('~/components/common/LoadingSpinner.vue'),
    { priority: 'high', preload: true }
  ),
  
  // Medium priority - frequently used components
  Modal: createOptimizedAsyncComponent(
    () => import('~/components/Modal/ModalProductDetail.vue'),
    { priority: 'medium' }
  ),
  
  ConfirmDialog: createOptimizedAsyncComponent(
    () => import('~/components/dialog/ConfirmDialog.vue'),
    { priority: 'medium' }
  ),
  
};

/**
 * Smart component loader based on viewport
 */
export const createViewportAwareComponent = (
  loader: () => Promise<any>,
  options: OptimizedAsyncOptions = {}
) => {
  return defineAsyncComponent({
    loader: async () => {
      // Only load if component is likely to be visible
      if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
        // Add intersection observer logic if needed
      }
      return await loader();
    },
    delay: options.delay || 100,
    timeout: options.timeout || 2000,
    loadingComponent: createLoadingComponent(options.priority || 'medium'),
    errorComponent: createErrorComponent(),
  });
};

/**
 * Component cache for reused components
 */
const componentCache = new Map();

export const createCachedAsyncComponent = (
  key: string,
  loader: () => Promise<any>,
  options: OptimizedAsyncOptions = {}
) => {
  if (componentCache.has(key)) {
    return componentCache.get(key);
  }
  
  const component = createOptimizedAsyncComponent(loader, options);
  componentCache.set(key, component);
  
  return component;
};

/**
 * Clear component cache
 */
export const clearComponentCache = () => {
  componentCache.clear();
};
