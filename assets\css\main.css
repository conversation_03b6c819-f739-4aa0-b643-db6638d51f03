@tailwind base;
@tailwind components;
@tailwind utilities;

/* Optimized Page Transitions */
.page-enter-active,
.page-leave-active {
  transition: opacity 150ms ease-in-out;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .page-enter-active,
  .page-leave-active {
    transition: none;
  }
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Optimize font loading */
@font-face {
  font-display: swap;
}

.group:hover .group-hover\:block {
  display: block;
}
.hover\:w-64:hover {
  width: 45%;
}

/* Custom Scrollbar Styles - Smaller and more elegant */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 6px; /* Vertical scrollbar width */
  height: 6px; /* Horizontal scrollbar height */
}

::-webkit-scrollbar-track {
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8; /* Darker gray on hover */
}

::-webkit-scrollbar-thumb:active {
  background: #64748b; /* Even darker when active */
}

/* Corner where horizontal and vertical scrollbars meet */
::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
}

/* For better mobile experience */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 2px;
  }

  ::-webkit-scrollbar-track {
    border-radius: 2px;
  }
}

/* Utility classes for different scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  border-radius: 2px;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Primary colored scrollbar */
.scrollbar-primary::-webkit-scrollbar-thumb {
  background: #3b82f6;
}

.scrollbar-primary::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

.scrollbar-primary {
  scrollbar-color: #3b82f6 #f1f5f9;
}

.option-message {
  @apply hidden group-hover:block flex-shrink-0 focus:outline-none mx-2 rounded-full text-gray-500 hover:text-gray-900 hover:bg-gray-700 bg-gray-800 w-8 h-8 p-2;
}
