// plugins/tippy.client.ts
import { defineNuxtPlugin } from "#app";
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light-border.css";

// Add custom CSS for rounded tooltips
if (process.client) {
  const style = document.createElement("style");
  style.textContent = `
    .tippy-box[data-theme~='light-border'] {
      border-radius: 8px !important;
    }
    .tippy-box[data-theme~='light-border'] .tippy-content {
      border-radius: 6px !important;
    }
    .tippy-box[data-theme~='light-border'] .tippy-arrow {
      color: #ffffff !important;
    }
  `;
  document.head.appendChild(style);
}

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive("tippy", {
    mounted(el, binding) {
      import("tippy.js").then(({ default: tippy }) => {
        // Xử lý trigger theo modifiers
        let trigger = "mouseenter focus"; // mặc định là hover

        if (binding.modifiers.click) {
          trigger = "click";
        } else if (binding.modifiers.hover) {
          trigger = "mouseenter focus";
        }

        // Xử lý placement từ binding hoặc dùng mặc định
        let placement: any = "top";
        if (binding.arg) {
          placement = binding.arg; // v-tippy:bottom-start="content"
        }

        tippy(el, {
          content: binding.value,
          allowHTML: true,
          theme: "light-border",
          placement: placement,
          interactive: true,
          trigger,
          delay: [100, 100],
          maxWidth: 350,
          appendTo: () => document.body, // Append to body để tránh bị ẩn bởi overflow
          zIndex: 9999, // Z-index cao để hiển thị trên các element khác
          popperOptions: {
            strategy: "fixed", // Sử dụng fixed positioning
          },
        });
      });
    },
  });
});
