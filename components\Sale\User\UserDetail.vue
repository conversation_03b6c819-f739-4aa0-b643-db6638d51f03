<template>
  <div class="user-detail-container">
    <div class="px-2 py-1 bg-white rounded-md ">
      <div class="flex items-center gap-1 mb-1">
        <span class="text-primary font-semibold">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
            />
          </svg>
        </span>
        <h3 class="text-sm font-semibold text-primary">Kh<PERSON>ch hàng</h3>
      </div>
      <div
        class="relative p-3 mt-1 border border-primary/20 rounded-lg bg-primary/5 hover:bg-primary/10 transition-colors duration-200"
      >
        <button
          @click="clearCustomer"
          class="absolute top-2 right-2 p-1 rounded-full hover:bg-red-100 focus:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-all duration-200 group"
          aria-label="Xóa khách hàng"
          type="button"
        >
          <svg
            class="w-4 h-4 text-red-500 group-hover:text-red-600 transition-colors duration-200"
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
              clip-rule="evenodd"
            />
          </svg>
        </button>

        <div class="pr-8">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <div
                class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-primary"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                  />
                </svg>
              </div>
            </div>

            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 text-sm">
                <span class="font-medium text-gray-900 capitalize truncate">
                  {{ customer.name }}
                </span>
                <span class="text-gray-400">|</span>
                <span class="text-gray-700 font-mono">
                  {{ customer.phone }}
                </span>
              </div>
            </div>

            <div class="flex-shrink-0">
              <button
                @click="toggleModalEditCustomer"
                class="p-2 text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1 transition-all duration-200 group"
                aria-label="Chỉnh sửa thông tin khách hàng"
                type="button"
              >
                <svg
                  class="w-4 h-4 group-hover:scale-110 transition-transform duration-200"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="isModalEditCustomer" class="">
    <ModalEditCustomer
      @closeModalEditCustomer="closeModalEditCustomer"
      @updateUserData="updateUserData"
      :customer="customer"
    />
  </div>
  <div v-if="isModalManageAddressShip" class="">
    <ModalManageAddressShip
      @closeModalManageAddressShip="handlePopup"
      :customer="customer"
    ></ModalManageAddressShip>
  </div>
</template>

<script setup lang="ts">
// ✅ Enhanced TypeScript Interfaces
interface Customer {
  name: string;
  phone: string;
  id?: string;
}

interface OrderDetail {
  order?: {
    customAttribute?: {
      exportVatInvoiceStatus?: string;
    };
  };
}

// ✅ Lazy Loading Components with Enhanced Error Handling
const ModalEditCustomer = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalEditCustomer.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-20 rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-4" }, "Failed to load modal"),
});

const ModalManageAddressShip = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalManageAddressShip.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-20 rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-4" }, "Failed to load modal"),
});

// ✅ Reactive State with TypeScript
const isModalEditCustomer = ref(false);
const isModalManageAddressShip = ref(false);

// ✅ Composables and Computed Properties
const orderStore = useOrderStore();
const customer = computed((): Customer => orderStore.customerInOrder);
const orderDetail = computed((): OrderDetail => orderStore.orderDetail);

// ✅ Enhanced Emits with TypeScript
const emits = defineEmits<{
  setValueShippingAddress: [value: string];
}>();

// ✅ Helper function to check if invoice is published
const isInvoicePublished = computed(() => {
  return (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  );
});

// ✅ Enhanced Clear Customer Function
const clearCustomer = () => {
  if (isInvoicePublished.value) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động"
    );
    return;
  }

  try {
    const value = "at-counter";
    emits("setValueShippingAddress", value);
    orderStore.clearCustomer();
  } catch (error) {
    console.error("Error clearing customer:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi xóa khách hàng");
  }
};

// ✅ Enhanced Modal Management Functions
const toggleModalEditCustomer = () => {
  if (isInvoicePublished.value) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động"
    );
    return;
  }

  isModalEditCustomer.value = true;
};

const closeModalEditCustomer = () => {
  isModalEditCustomer.value = false;
};

const handlePopup = (value: boolean) => {
  isModalManageAddressShip.value = value;
};

// ✅ Update User Data Handler
const updateUserData = (updatedCustomer: Customer) => {
  try {
    // Update customer in store (assuming the store has a method to update customer)
    orderStore.customerInOrder = updatedCustomer;
    useNuxtApp().$toast.success("Cập nhật thông tin khách hàng thành công");
    closeModalEditCustomer();
  } catch (error) {
    console.error("Error updating customer:", error);
    useNuxtApp().$toast.error(
      "Có lỗi xảy ra khi cập nhật thông tin khách hàng"
    );
  }
};
</script>

<style scoped>
/* ✅ Enhanced Component Styles with Tailwind CSS */

/* Custom animations for smooth interactions */
.user-detail-container {
  @apply transition-all duration-200 ease-in-out;
}

/* Custom focus styles for better accessibility */
button:focus-visible {
  @apply ring-2 ring-offset-2 outline-none;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Custom scrollbar for modal content */
.modal-content::-webkit-scrollbar {
  width: 4px;
}

.modal-content::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.modal-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Loading animation enhancement */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .user-detail-container {
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }

  .customer-card {
    padding: 0.5rem;
  }

  .customer-info {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .user-detail-container {
    color: rgb(243 244 246);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .user-detail-container {
    border: 2px solid black;
  }

  button {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .animate-pulse {
    animation: none;
    transition: none;
  }
}
</style>
