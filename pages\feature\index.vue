<template>
  <div
    class="h-[calc(100vh-56px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 mt-[56px] overflow-y-auto"
  >
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -inset-10 opacity-20">
        <div
          class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"
        ></div>
        <div
          class="absolute top-3/4 left-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-2000"
        ></div>
      </div>
    </div>

    <!-- Header Section -->
    <div class="relative z-10 pt-8 pb-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Navigation & Title -->
        <div class="flex items-center justify-between mb-8">
          <!-- Back Button -->
          <button
            @click="handleClickBack"
            class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors duration-200 group"
          >
            <svg
              class="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="font-medium">Quay lại</span>
          </button>

          <!-- Page Title -->
          <h1
            class="text-3xl font-bold text-gray-900 absolute left-1/2 transform -translate-x-1/2"
          >
            Chọn tính năng
          </h1>

          <!-- Spacer to balance the layout -->
          <div class="w-24"></div>
        </div>

        <!-- Description Section -->
        <div class="text-center mb-12">
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Chọn tính năng bạn muốn sử dụng để quản lý cửa hàng hiệu quả
          </p>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="relative z-10 pb-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Loading State -->
        <div
          v-if="isLoading"
          class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6"
        >
          <div v-for="i in 10" :key="`skeleton-${i}`" class="animate-pulse">
            <div
              class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg h-32"
            >
              <div class="flex flex-col items-center space-y-3">
                <div class="w-12 h-12 bg-gray-300 rounded-xl"></div>
                <div class="h-4 bg-gray-300 rounded w-16"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Features Grid -->
        <div
          v-else-if="menu?.length > 0"
          class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6"
        >
          <div
            v-for="(item, index) in menu"
            :key="`feature-${index}`"
            class="group"
          >
            <div
              @click="handleNavigate(item)"
              class="relative bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer transform hover:scale-105 hover:-translate-y-2"
            >
              <!-- Feature Icon -->
              <div class="flex flex-col items-center space-y-4">
                <div class="relative">
                  <div
                    class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  >
                    <div
                      v-html="item.icon"
                      class="text-white [&>svg]:w-8 [&>svg]:h-8 [&>svg]:stroke-current"
                    ></div>
                  </div>
                  <!-- Feature Badge -->
                  <div
                    v-if="getFeatureBadge(item)"
                    class="absolute -top-2 -right-2"
                  >
                    <div
                      class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center"
                    >
                      <span class="text-xs font-bold text-white">{{
                        getFeatureBadge(item)
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- Feature Name -->
                <div class="text-center">
                  <h3
                    class="text-sm font-bold text-gray-900 group-hover:text-primary transition-colors duration-200"
                  >
                    {{ item.name }}
                  </h3>
                  <p
                    v-if="getFeatureDescription(item)"
                    class="text-xs text-gray-500 mt-1"
                  >
                    {{ getFeatureDescription(item) }}
                  </p>
                </div>
              </div>

              <!-- Hover Effect -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-primary/5 to-blue-500/5 rounded-2xl opacity-0 transition-opacity duration-300"
              ></div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading" class="text-center py-16">
          <div
            class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <svg
              class="w-12 h-12 text-gray-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
          <h3 class="text-xl font-medium text-gray-900 mb-2">
            Không có tính năng nào
          </h3>
          <p class="text-gray-500 mb-6">
            Bạn chưa được cấp quyền truy cập vào tính năng nào.
          </p>
          <button
            @click="handleRefresh"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary/90 transition-colors duration-200"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd"
              ></path>
            </svg>
            Làm mới
          </button>
        </div>
      </div>
    </div>

    <!-- Full Screen Loading -->
    <Teleport to="body">
      <LoadingSpinner v-if="isNavigating" />
    </Teleport>
  </div>
</template>

<script setup>
import { menuItems } from "@/utils/dataMenuLeft";
import { Teleport } from "vue";

// SEO
useHead({
  title: "Chọn tính năng",
  meta: [
    {
      name: "description",
      content: "Chọn tính năng để quản lý cửa hàng",
    },
  ],
});

// Page config
definePageMeta({
  layout: "custom",
  middleware: ["auth", "permission", "store"],
});

// Reactive state
const auth = useCookie("auth").value;

// Use tab-isolated context instead of cookies
const { storeId, orgId } = useTabContext();
const menu = ref([]);
const isLoading = ref(false);
const isNavigating = ref(false);
const selectedFeature = ref(null);

// Removed storeName computed - no longer needed

// Feature utility functions
const getFeatureDescription = (item) => {
  const descriptions = {
    "Nhật ký": "Nhật ký bán hàng",
    "Bán hàng": "Tạo đơn bán hàng",
    "Đơn hàng": "Quản lý đơn hàng",
    "Khách hàng": "Quản lý khách hàng",
    "Chấm công": "Quản lý chấm công",
    "Quản lý thu chi": "Quản lý thu chi",
    "Báo cáo": "Thống kê doanh thu",
    "Sản phẩm": "Quản lý sản phẩm",
    Chat: "Hỗ trợ khách hàng",
    "Quản lý PTTT": "Quản lý phương thức thanh toán",
  };

  // Fallback: thử cả tên gốc và tên đã trim
  return descriptions[item.name] || descriptions[item.name.trim()] || "";
};

const getFeatureBadge = (item) => {
  // Show badge for important features
  const badges = {
    "Bán hàng": "HOT",
    "Quản lý thu chi": "NEW",
    "Quản lý PTTT": "NEW",
  };
  return badges[item.name] || null;
};

// Enhanced navigation with loading states
const handleNavigate = async (item) => {
  if (isNavigating.value) return; // Prevent multiple clicks

  isNavigating.value = true;

  try {
    // Immediate navigation without delay
    await navigateTo(
      `${item.to}?orgId=${orgId.value}&storeId=${storeId.value}`
    );
  } catch (error) {
    console.error("Navigation error:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi chuyển trang");
  } finally {
    isNavigating.value = false;
    selectedFeature.value = null;
  }
};

// Filter features based on user roles
const handleFeatureForRole = () => {
  isLoading.value = true;
  try {
    const response = menuItems.value.filter((item) =>
      item.roles?.some((role) => auth?.user?.roles?.includes(role))
    );
    menu.value = response || [];
  } catch (error) {
    console.error("Error filtering features:", error);
    menu.value = [];
  } finally {
    isLoading.value = false;
  }
};

// Navigation
const handleClickBack = () => {
  navigateTo(`/dashboard?orgId=${orgId.value}`);
};

// Refresh features
const handleRefresh = () => {
  handleFeatureForRole();
};

// Initialize on mount
onMounted(() => {
  handleFeatureForRole();
});
</script>

<style scoped>
/* Custom animations */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes feature-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

@keyframes badge-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Apply animations */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient-shift 15s ease infinite;
}

.group:hover .w-16.h-16 {
  animation: feature-glow 2s ease-in-out infinite;
}

/* Feature badge animation */
.absolute.-top-2.-right-2 > div {
  animation: badge-pulse 2s ease-in-out infinite;
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background-color: rgba(255, 255, 255, 0.9);
  }
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.8);
  }
}

/* Custom icon styling */
.group [v-html] svg {
  transition: all 0.3s ease;
}

.group:hover [v-html] svg {
  transform: scale(1.1);
}

/* Focus styles for accessibility */
.group > div:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Loading overlay animation */
.absolute.inset-0 {
  backdrop-filter: blur(4px);
}

/* Full screen loading overlay */
.fixed.inset-0 {
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Loading modal animation */
.fixed .bg-white {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Feature card entrance animation */
.group {
  opacity: 1;
  transform: translateY(0);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .hover\:scale-105:hover {
    transform: scale(1.02);
  }

  .hover\:-translate-y-2:hover {
    transform: translateY(-4px);
  }

  /* Reduce grid gaps on mobile */
  .grid.gap-6 {
    gap: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/80 {
    background-color: rgba(255, 255, 255, 0.95);
  }

  .border-white\/20 {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bg-gradient-to-br {
    animation: none;
  }

  .group:hover .w-16.h-16 {
    animation: none;
  }

  .absolute.-top-2.-right-2 > div {
    animation: none;
  }

  .transition-all,
  .transition-colors,
  .transition-opacity,
  .transition-shadow,
  .transition-transform {
    transition: none;
  }
}

/* Custom scrollbar for overflow areas */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
</style>
