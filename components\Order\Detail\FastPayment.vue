<template>
  <div class="bg-white p-2 rounded">
    <!-- Header -->
    <h3 class="text-lg font-semibold text-primary">Thông tin thanh toán</h3>

    <!-- Payment Details -->
    <div class="text-sm">
      <!-- Vouchers/Discounts -->
      <div v-if="vouchers && vouchers.length > 0" class="space-y-1">
        <div class="text-gray-600 font-medium">Mã giảm giá áp dụng:</div>
        <div v-for="voucher in vouchers" :key="voucher.id" class="ml-2">
          <div class="flex justify-between items-center py-1">
            <span class="text-gray-700">{{
              voucher.voucherCode || voucher.title
            }}</span>
            <span class="text-red-600 font-medium">
              -{{ formatCurrency(voucher.value?.amount || 0) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Member Level Discount -->
      <div v-if="memberLevel" class="flex justify-between items-center py-1">
        <span class="text-gray-600">Chiết khấu thành viên:</span>
        <span class="text-red-600 font-medium">
          -{{ formatCurrency(memberLevel.value?.amount || 0) }}
        </span>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-1"></div>

      <!-- Subtotal -->
      <div class="flex justify-between items-center py-1">
        <span class="text-gray-600 font-medium">Tổng tiền hàng:</span>
        <span class="text-gray-900 font-medium">
          {{ formatCurrency(subtotalAmount) }}
        </span>
      </div>

      <!-- Total Discount -->
      <div class="flex justify-between items-center py-1">
        <span class="text-gray-600 font-medium">Tổng giảm giá:</span>
        <span class="text-red-600 font-medium">
          -{{ formatCurrency(totalDiscountAmount) }}
        </span>
      </div>

      <!-- After Discount -->
      <div class="flex justify-between items-center py-1">
        <span class="text-gray-600 font-medium">Sau giảm giá:</span>
        <span class="text-gray-900 font-medium">
          {{ formatCurrency(afterDiscountAmount) }}
        </span>
      </div>

      <!-- VAT -->
      <div v-if="vatAmount > 0" class="flex justify-between items-center py-1">
        <div class="space-x-1">
          <span class="text-gray-600 font-medium">VAT:</span>
          <span
            v-if="vatAmount > 0"
            ref="tooltipTargetRef"
            v-tippy.click="tooltipContent"
            class="text-xs text-primary cursor-pointer"
          >
            (xem chi tiết)
          </span>
        </div>
        <span class="text-gray-900 font-medium">
          {{ formatCurrency(vatAmount) }}
        </span>
      </div>

      <!-- Shipping Fee -->
      <div
        v-if="shippingFee > 0"
        class="flex justify-between items-center py-1"
      >
        <span class="text-gray-600 font-medium">Phí vận chuyển:</span>
        <span class="text-gray-900 font-medium">
          {{ formatCurrency(shippingFee) }}
        </span>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-2"></div>

      <!-- Total Amount -->
      <div
        class="flex justify-between items-center py-1 bg-gray-50 rounded"
      >
        <span class="text-gray-900 font-semibold">Tổng đơn hàng:</span>
        <span class="text-primary font-semibold">
          {{ formatCurrency(totalAmount) }}
        </span>
      </div>

      <!-- Already Paid -->
      <div
        v-if="alreadyPaid > 0"
        class="flex justify-between items-center py-1"
      >
        <span class="text-gray-600 font-semibold">Đã thanh toán:</span>
        <span class="text-green-600 font-semibold">
          {{ formatCurrency(alreadyPaid) }}
        </span>
      </div>

      <!-- Remaining Amount -->
      <div
        v-if="remainingAmount > 0"
        class="flex justify-between items-center py-2 bg-red-50 rounded"
      >
        <span class="text-gray-900 font-semibold">Còn nợ:</span>
        <span class="text-red-600 font-semibold">
          {{ formatCurrency(remainingAmount) }}
        </span>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["order"]);

// Import utils
import { formatCurrency } from "~/utils/formatCurrency";

// Computed properties for payment information
const vouchers = computed(() => {
  return (
    props.order?.order?.discountApplications?.filter(
      (item) => item.type === "VOUCHER"
    ) || []
  );
});

const memberLevel = computed(() => {
  return props.order?.order?.discountApplications?.find(
    (item) => item.type === "MEMBER"
  );
});

// Payment amounts
const subtotalAmount = computed(() => {
  return props.order?.order?.subtotalPrice?.amount || 0;
});

const totalDiscountAmount = computed(() => {
  const subtotal = subtotalAmount.value;
  const afterDiscount =
    props.order?.order?.discountTotalPrice?.amount || subtotal;
  return subtotal - afterDiscount;
});

const afterDiscountAmount = computed(() => {
  return props.order?.order?.discountTotalPrice?.amount || subtotalAmount.value;
});

const vatAmount = computed(() => {
  return props.order?.order?.totalVAT?.amount || 0;
});

const shippingFee = computed(() => {
  return props.order?.order?.shippingFee?.amount || 0;
});

const totalAmount = computed(() => {
  return props.order?.order?.totalPrice?.amount || 0;
});

const alreadyPaid = computed(() => {
  return props.order?.totalAlreadyPaid || 0;
});

const remainingAmount = computed(() => {
  const total = totalAmount.value;
  const paid = alreadyPaid.value;
  const remaining = total - paid;
  return remaining > 0 ? remaining : 0;
});

// Tooltip for VAT details
const tooltipTargetRef = ref(null);

const tooltipContent = computed(() => {
  const items = props.order?.activeOrderItemProfiles || [];
  const itemsHtml = items
    .map((item) => {
      const orderLineItem = item?.orderLineItem;
      const variantId = orderLineItem?.variant?.id || "";
      const sku = orderLineItem?.variant?.sku || "";
      const vatRate = orderLineItem?.vatRate?.amount || 0;
      const totalVAT = orderLineItem?.totalVAT?.amount || 0;

      return `
        <div class="grid grid-cols-[3fr_1fr_1fr] gap-2 text-sm text-gray-800 mb-1">
          <div class="overflow-hidden text-ellipsis whitespace-nowrap">
            ${variantId} - ${sku}
          </div>
          <div class="text-center whitespace-nowrap">
            ${vatRate}%
          </div>
          <div class="text-right whitespace-nowrap">
            ${formatCurrency(totalVAT)}
          </div>
        </div>
      `;
    })
    .join("");

  return `
    <div class="bg-white p-2 rounded-lg text-sm max-w-xs space-y-1">
      <div class="font-semibold text-primary mb-1">Chi tiết VAT</div>

      <div class="grid grid-cols-[3fr_1fr_1fr] font-semibold text-gray-700 border-b pb-2">
        <div>ID - SKU</div>
        <div class="text-center">VAT</div>
        <div class="text-center">Tiền VAT</div>
      </div>

      <div class="pt-2 space-y-1">
        ${itemsHtml}
      </div>

      <hr class="border-t border-gray-300 my-2" />

      <div class="flex items-center justify-between">
        <span class="font-semibold text-gray-600">Tổng VAT</span>
        <span class="ml-4 block font-medium text-primary">
          ${formatCurrency(vatAmount.value)}
        </span>
      </div>
    </div>
  `;
});

// Watch để cập nhật tooltip khi data thay đổi
watch(
  () => props.order,
  (newVal) => {
    if (newVal && tooltipTargetRef.value?._tippy) {
      tooltipTargetRef.value._tippy.setContent(tooltipContent.value);
    }
  },
  { deep: true }
);
</script>
