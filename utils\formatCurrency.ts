export const formatCurrency = (value: number) => {
  const formatter = new Intl.NumberFormat("vi-VN", {
    currency: "VND",
  });
  return formatter.format(value) + " đ";
};
export const formatCurrencyV2 = (value: number) => {
  const formatter = new Intl.NumberFormat("vi-VN", {
    currency: "VND",
  });
  return formatter.format(value);
};
export const formatCurrencyNoSymbol = (value: number) => {
  const formatter = new Intl.NumberFormat("vi-VN", {
    currency: "VND",
  });
  return formatter.format(value);
};
