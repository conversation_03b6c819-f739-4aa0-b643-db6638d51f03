<template>
  <!-- ✅ Optimized ProductCategoryCard Component with Tailwind CSS -->
  <article
    :class="cardClasses"
    data-testid="product-card-vertical"
    @click="addProduct(product)"
    @keydown.enter="addProduct(product)"
    @keydown.space.prevent="addProduct(product)"
    role="button"
    :aria-label="`Thêm sản phẩm ${product.title} vào giỏ hàng`"
    tabindex="0"
  >
    <!-- ✅ Enhanced Image Container -->
    <div class="relative overflow-hidden group">
      <!-- ✅ Product Image with Loading States -->
      <NuxtImg
        data-testid="image-slot"
        :alt="`Hình ảnh sản phẩm ${product.title}`"
        :src="handleGetImageProductUrl()"
        :class="imageClasses"
        loading="lazy"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <!-- ✅ Image Loading Placeholder -->
      <div
        v-if="imageLoading"
        class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
      >
        <svg
          class="w-4 h-4 md:w-6 md:h-6 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <!-- ✅ Image Error Fallback -->
      <div
        v-if="imageError"
        class="absolute inset-0 bg-gray-100 flex items-center justify-center"
      >
        <div class="text-center">
          <svg
            class="w-4 h-4 md:w-6 md:h-6 text-gray-400 mx-auto mb-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-xs text-gray-500 hidden md:block"
            >Không có ảnh</span
          >
        </div>
      </div>

      <!-- ✅ Hover Overlay Effect -->
      <div
        class="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-all duration-300"
      ></div>

      <!-- ✅ Product Type Badge for Variable Products -->
      <div v-if="product.subType === 'VARIABLE'" class="absolute top-1 right-1">
        <span
          class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
        >
          <svg class="w-2 h-2 mr-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
            />
          </svg>
          <span class="hidden md:inline">Biến thể</span>
        </span>
      </div>

      <!-- ✅ Loading State Indicator -->
      <Transition
        enter-active-class="transition-opacity duration-200"
        leave-active-class="transition-opacity duration-200"
        enter-from-class="opacity-0"
        leave-to-class="opacity-0"
      >
        <div
          v-if="isLoading"
          class="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center"
        >
          <div
            class="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-primary"
          ></div>
        </div>
      </Transition>
    </div>

    <!-- ✅ Enhanced Product Information -->
    <div :class="contentClasses">
      <!-- ✅ Product Title with Smart Truncation -->
      <div class="flex-shrink-0">
        <h3 :class="titleClasses" data-testid="link" :title="product.title">
          {{ truncatedTitle }}
        </h3>
      </div>

      <!-- ✅ Enhanced Price Section -->
      <div class="flex-shrink-0 mt-auto">
        <div class="flex items-center justify-between">
          <span :class="priceClasses">
            {{ displayPrice }}
          </span>

          <!-- ✅ Add to Cart Icon (visible on hover) -->
          <div
            class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 hidden md:block"
          >
            <div
              class="w-4 h-4 bg-primary rounded-full flex items-center justify-center"
            >
              <svg
                class="w-2 h-2 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="3"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- ✅ Product ID (compact display) -->
        <div class="text-xs text-gray-500 truncate mt-0.5 hidden md:block">
          ID: {{ product.id }}
        </div>
      </div>
    </div>
  </article>

  <!-- ✅ Enhanced Modal with Teleport -->
  <Teleport to="body" v-if="isModalOpen">
    <Transition name="modal">
      <ModalProductDetail
        :isOpen="isModalOpen"
        :productId="product.id"
        @close="closeModal"
      />
    </Transition>
  </Teleport>
</template>

<script setup>
// ✅ Optimized ProductCategoryCard Component Script
// Lazy load heavy components with enhanced error handling
const ModalProductDetail = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalProductDetail.vue"),
  delay: 200,
  timeout: 3000,
  errorComponent: () =>
    h("div", { class: "text-red-500 p-2 text-xs" }, "Failed to load modal"),
});

// Props with TypeScript validation
const props = defineProps({
  product: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

// Composables
const { addProductToOrder } = useOrderStore();
const { getImageProducrUrl } = usePortal();

// Reactive state
const isModalOpen = ref(false);
const isLoading = ref(false);
const imageLoading = ref(true);
const imageError = ref(false);

// Computed properties for better performance
const displayPrice = computed(() => {
  return props.product.price !== null && props.product.price !== undefined
    ? formatCurrency(props.product.price)
    : "Chưa có giá";
});

// ✅ Smart text truncation for product title
const truncatedTitle = computed(() => {
  const title = props.product.title || "";
  const maxLength = 40; // Maximum characters for mobile
  const maxLengthDesktop = 60; // Maximum characters for desktop

  // For very long titles, truncate intelligently
  if (title.length > maxLength) {
    // Try to break at word boundaries
    const words = title.split(" ");
    let truncated = "";

    for (const word of words) {
      if ((truncated + word).length > maxLength) {
        break;
      }
      truncated += (truncated ? " " : "") + word;
    }

    return truncated || title.substring(0, maxLength);
  }

  return title;
});

// Computed classes for better maintainability and responsive design
const cardClasses = computed(() => [
  "relative border cursor-pointer bg-white rounded-lg overflow-hidden",
  "flex flex-col w-[85px] md:w-[118px] h-[120px] md:h-[180px]", // ✅ Fixed height for consistency
  "hover:shadow-lg hover:border-gray-300 hover:-translate-y-0.5",
  "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50",
  "transition-all duration-300 ease-out",
  "transform hover:scale-[1.02] active:scale-[0.98]",
  "group border-gray-200",
]);

const imageClasses = computed(() => [
  "object-contain w-full h-[40px] md:h-[86px]",
  "transition-all duration-300",
  "group-hover:scale-105",
  { "opacity-0": imageLoading.value },
]);

const contentClasses = computed(() => [
  "p-1.5 md:p-2 border-t border-gray-200 flex-1 flex flex-col justify-between",
  "bg-white group-hover:bg-gray-50",
  "transition-colors duration-200",
]);

const titleClasses = computed(() => [
  "text-xs md:text-sm text-gray-900 font-medium leading-3 md:leading-4",
  "line-clamp-2 hover:text-primary transition-colors duration-200",
  "group-hover:text-primary-600",
  "h-6 md:h-8 overflow-hidden break-words text-ellipsis", // ✅ Enhanced truncation
]);

const priceClasses = computed(() => [
  "font-bold text-xs md:text-sm text-primary",
  "whitespace-nowrap",
]);

// Methods
const handleGetImageProductUrl = () => {
  try {
    return getImageProducrUrl(props.product.id, "PRODUCT");
  } catch (error) {
    console.error("Error getting image URL:", error);
    return "/placeholder-image.jpg"; // Fallback image
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};

const addProduct = async () => {
  try {
    if (props.product.subType !== "VARIABLE") {
      isLoading.value = true;
      await addProductToOrder(props.product);
      useNuxtApp().$toast.success(
        `Đã thêm ${props.product.title} vào giỏ hàng`
      );
    } else {
      // Variable product - open modal for variant selection
      isModalOpen.value = true;
    }
  } catch (error) {
    console.error("Error adding product:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi thêm sản phẩm");
  } finally {
    isLoading.value = false;
  }
};

const closeModal = () => {
  isModalOpen.value = false;
};

// Lifecycle
onMounted(() => {
  // Preload image
  const img = new Image();
  img.onload = handleImageLoad;
  img.onerror = handleImageError;
  img.src = handleGetImageProductUrl();
});
</script>

<style scoped>
/* ✅ Enhanced text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* ✅ Fallback for browsers that don't support line-clamp */
@supports not (-webkit-line-clamp: 2) {
  .line-clamp-2 {
    display: block;
    max-height: 2.4em; /* Approximate height for 2 lines */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* ✅ Modal transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* ✅ Enhanced hover effects */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* ✅ Focus styles for accessibility */
[role="button"]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 8px;
}

/* ✅ Smooth image loading */
img {
  transition: opacity 0.3s ease;
}

/* ✅ Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ✅ Pulse animation for loading placeholder */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ✅ Enhanced card hover effects */
.group:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ✅ Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* ✅ CSS containment for performance */
.bg-white {
  contain: layout style;
}

/* ✅ Compact responsive design */
@media (max-width: 768px) {
  .group:hover {
    transform: none; /* Disable hover effects on mobile for better touch experience */
  }
}
</style>
