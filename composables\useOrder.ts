import { useNuxtApp, useRoute, useRouter } from "#app";
import { ref, computed, watch } from "vue";
import type { ShippingAddress } from "~/types/ShippingAddress";
import type { employeesQuery } from "@longvansoftware/storefront-js-client/dist/src/types/user";
import type {
  DiscountCampaign,
  MemberDiscount,
} from "@longvansoftware/storefront-js-client/dist/src/types/order";
import type { Auth } from "~/types/Auth";
import store from "~/middleware/store";
export default function useOrder() {
  const { $sdk } = useNuxtApp();
  // const token = useCookie("token").value;
  // if (token) {
  //   $sdk.setToken(token);
  // } else {
  //   throw new Error("Token is not defined");
  // }

  const route = useRoute();
  const router = useRouter();

  const searchOrderId = ref(route.query.orderId || "");
  const searchUser = ref(route.query.user || "");
  const currentPage = ref<number>(parseInt(route.query.page as string) || 1);
  const searchProductId = ref(route.query.product || "");
  const paymentMethodId = ref(route.query.payment || "");
  const employeeId = ref(route.query.employeeId || "");
  const activeTab = ref(route.query.status || "");
  const startDate = ref(route.query.startDate || "");
  const endDate = ref(route.query.endDate || "");
  const fulfillment = ref(route.query.fulfillment || "");
  const itemsPerPage = 30;

  const createOrderTemp = async (order: any) => {
    try {
      const response = await $sdk.order.createOrderTemp(order, "WEB");
      return response;
    } catch (error) {
      console.error("Error creating createOrderTemp:", error);
    }
  };
  const createOrder = async (orderData: any, created_by: string) => {
    try {
      const response = await $sdk.order.createOrder(
        orderData,
        "WEB",
        false,
        created_by
      );

      return response;
    } catch (error) {
      console.error("Error creating createOrder:", error);
    }
  };
  const useOrders = useOrdersStore();
  const orderStore = useOrderStore();
  const loading = ref(false);
  const createOrders = async () => {
    loading.value = true;
    const data: any = {
      date_create_from: Date.now().toString(),
      currentPage: 1,
      maxResult: 5,
      status: [10],
    };
    try {
      const response = await $sdk.order.createOrderTemp(
        {
          status: "DRAFT",
          orderType: "POS_SALE",
          platform: "WEB",
          time: new Date().getTime(),
        },
        "WEB"
      );
      await updateStatusOpen(response.data?.orderId);
      await orderStore.getListOrder(data);
      loading.value = false;
      router.push({
        path: "/sale",
        query: {
          ...router.currentRoute.value.query,
          orderId: response.data.orderId,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating order:", error);
    }
  };
  /// tạo đơn mua
  const createBuyOrder = async () => {
    const auth = useCookie("auth").value as unknown as Auth;
    loading.value = true;
    try {
      const response = await $sdk.order.createOrderTemp(
        {
          status: "DRAFT",
          orderType: "POS_SALE",
          platform: "WEB",
          time: new Date().getTime(),
        },
        "WEB"
      );
      await Promise.all([
        updateOrderCustomer(response.data?.orderId, auth?.user?.id, ""),
        updateStatusOpen(response.data?.orderId),
      ]);

      loading.value = false;
      router.push({
        path: "/buy/detail",
        query: {
          ...router.currentRoute.value.query,
          orderId: response.data.orderId,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating order:", error);
    }
  };

  const fetchListSellOrder = async (requestData: any) => {
    try {
      const response = await $sdk.order.getListSellOrder(requestData);
      return response;
    } catch (error) {
      console.error("Error fetching sales orders:", error);
      throw error;
    }
  };
  const fetchListSellOrderV2 = async (requestData: any, storeId: string) => {
    try {
      const response = await $sdk.order.getListSellOrderV2(
        requestData,
        storeId
      );
      return response;
    } catch (error) {
      console.error("Error fetching sales orders:", error);
      throw error;
    }
  };
  const fetchListSellOrderAll = async (requestData: any) => {
    try {
      const response = await $sdk.order.getListSellOrderAll(requestData);
      return response;
    } catch (error) {
      console.error("Error fetching sales orders:", error);
      throw error;
    }
  };

  const addOrderLineItems = async (orderId: string, lineItems: any[]) => {
    try {
      const response = await $sdk.order.addOrderLineItems(orderId, lineItems);
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.error(response?.message);
      }
      return response;
    } catch (error) {
      console.log(`Error in addOrderLineItem: ${error}`);
      throw error;
    }
  };

  const getInforSellOrder = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoSellOrder(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const fetchOrderReturnDetails = async (orderId: string) => {
    try {
      const response = await getInfoSellOrderReturn(orderId);
      return response;
    } catch (error) {
      console.error("Error fetching order details:", error);
    }
  };
  const disableOrderItem = async (orderId: string, orderItemId: string) => {
    try {
      const response = await $sdk.order.cancelProductInOrder(
        orderId,
        orderItemId,
        "không phù hợp"
      );
      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.warning(`${error?.message}`);
      throw error;
    }
  };
  const removeProductInOrder = async (
    orderId: string,
    orderItemId: string,
    reason: string
  ) => {
    try {
      const response = await $sdk.order.removeProductInOrder(
        orderId,
        orderItemId,
        reason
      );
      return response;
    } catch (error) {
      throw error;
    }
  };

  const removeDraftOrder = async (orderId: string, updatedBy: string) => {
    try {
      const response = await $sdk.order.removeDraftOrder(orderId, updatedBy);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const fetchListSellOrderReturn = async (requestData: any) => {
    try {
      const response = await $sdk.order.getListReturnOrder(requestData);
      return response;
    } catch (error) {
      console.error("Error fetching sales orders:", error);
      throw error;
    }
  };
  const fetchListSellOrderReturnV2 = async (
    requestData: any,
    storeId: string
  ) => {
    try {
      const response = await $sdk.order.getListReturnOrderV2(
        requestData,
        storeId
      );
      return response;
    } catch (error) {
      console.error("Error fetching sales orders:", error);
      throw error;
    }
  };
  const fetchListPaymentMethod = async () => {
    try {
      const response = await $sdk.payment.getPaymentMethod();
      return response;
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      throw error;
    }
  };

  const fetchListSaleOrderStatus = async () => {
    try {
      const response = await $sdk.order.getListSaleOrderStatus();
      return response;
    } catch (error) {
      console.error("Error fetching sale order statuses:", error);
      throw error;
    }
  };

  const getInfoSellOrderReturn = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoReturnOrder(orderId);
      return response;
    } catch (error) {
      console.error("Error fetching order info:", error);
      throw error;
    }
  };

  const fetchDataEmployees = async () => {
    try {
      const response = await $sdk.user.getEmployeesByStoreChannelId();
      return response;
    } catch (error) {
      console.error("Error fetchDataEmployees info:", error);
      throw error;
    }
  };
  const getInfoSellOrder = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoSellOrder(orderId);
      return response;
    } catch (error) {
      console.error("Error fetching order info:", error);
      throw error;
    }
  };
  const fetchOrderDetails = async (orderId: string) => {
    try {
      const response = await getInfoSellOrder(orderId);
      return response;
    } catch (error) {
      console.error("Error fetching order details:", error);
    }
  };
  const fetchNotes = async (orderId: string) => {
    try {
      const response = await fetchOrderNote(orderId);
      console.log(response);
      return response;
    } catch (error) {
      console.error("Error fetching order notes:", error);
    }
  };
  const fetchShipping = async () => {
    try {
      // const response = await $sdk.order.getListShippingService();
      // return response;
    } catch (error) {
      console.error("Error fetching order info:", error);
      throw error;
    }
  };
  const getOrderPromotion = async (memberLevel: string) => {
    try {
      const response = await $sdk.order.getOrderPromotion(memberLevel);
      return response;
    } catch (error) {
      console.error("Error fetching order info:", error);
      throw error;
    }
  };
  const fetchOrderNote = async (orderId: string) => {
    try {
      const response = await $sdk.order.getListNoteWithoutLogin(orderId);
      return response;
    } catch (error) {
      console.error("Error fetching order notes:", error);
      throw error;
    }
  };

  const createNote = async (
    orderId: string,
    createdBy: string,
    noteContent: string
  ) => {
    try {
      const response = await $sdk.order.createNoteWithoutLogin(
        orderId,
        createdBy,
        noteContent
      );
      return response;
    } catch (error) {
      console.error("Error creating note:", error);
      throw error;
    }
  };

  const deleteNote = async (
    orderId: string,
    noteId: string,
    createdBy: string
  ) => {
    try {
      const response = await $sdk.order.deleteNoteWithoutLogin(
        orderId,
        noteId,
        createdBy
      );
      return response;
    } catch (error) {
      console.error("Error deleting note:", error);
      throw error;
    }
  };

  const fetchListOrderRelations = async (orderIds: string[]) => {
    try {
      const response = await $sdk.order.getListOrderRelationsWithoutLogin(
        orderIds
      );
      return response;
    } catch (error) {
      console.error("Error fetching order relations:", error);
      throw error;
    }
  };

  const updateStatusApproved = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(
        orderId,
        "APPROVED"
      );
      const data = {
        date_create_from: Date.now().toString(),
        currentPage: 1,
        maxResult: 5,
        status: [10],
      };
      orderStore.getListOrder(data);
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateStatusApprovedV2 = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(
        orderId,
        "APPROVED"
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateEmployee = async (
    orderId: string,
    saleId: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateSaleEmployee(
        orderId,
        saleId,
        updatedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateSaleEmployee = async (
    orderId: string,
    saleId: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateSaleEmployee(
        orderId,
        saleId,
        updatedBy
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      console.error("Error updateSaleEmployee:", error);
      throw error;
    }
  };
  const updateInfoCampaignPromotion = async (
    orderId: string,
    requestData: any
  ) => {
    try {
      const response = await $sdk.order.updateInfoCampaignPromotion(
        orderId,
        requestData
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };

  const updateStatusCancelled = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(
        orderId,
        "CANCELLED"
      );
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateStatusOpen = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(orderId, "OPEN");
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateStatusNewOrder = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(
        orderId,
        "COMPLETED"
      );
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateStatusCompleted = async (orderId: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(
        orderId,
        "COMPLETED"
      );
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };

  const updateStatusReturnOrderCompleted = async (
    orderId: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateStatusReturnOrder(
        orderId,
        "COMPLETED",
        updatedBy
      );
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateStatusReturnOrder = async (
    orderId: string,
    statusNew: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateStatusReturnOrder(
        orderId,
        statusNew,
        updatedBy
      );
      return response;
    } catch (error) {
      console.error("Error updating status:", error);
      throw error;
    }
  };
  const updateQuantityProductInOrder = async (
    orderId: any,
    orderItemId: string,
    quantity: number
  ) => {
    try {
      const response = await $sdk.order.updateQuantityProductInOrder(
        orderId,
        orderItemId,
        quantity
      );
      // setDiaryChange();
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.error(response?.message);
      }
      return response;
    } catch (error) {
      console.error("Error updateQuantityProductInOrder:", error);
      throw error;
    }
  };

  const updateShippingFee = async (orderId: string, shipingFee: number) => {
    try {
      const response = await $sdk.order.updateShippingFee(orderId, shipingFee);
      // useNuxtApp().$toast.success(`${response?.message}`);
      return response;
    } catch (error) {
      console.error("Error updateQuantityProductInOrder:", error);
      throw error;
    }
  };

  const updatePriceInOrder = async (
    orderId: string,
    orderItemId: string,
    priceNew: number,
    reason: string
  ) => {
    try {
      const response = await $sdk.order.updatePriceInOrder(
        orderId,
        orderItemId,
        priceNew,
        reason
      );
      return response;
    } catch (error) {
      console.error("Error updatePriceInOrder:", error);
      throw error;
    }
  };
  const updateDiscountCart = async (
    orderId: string,
    updated_by: string,
    requestData: any
  ) => {
    try {
      const response = await $sdk.order.updateDiscount(
        orderId,
        updated_by,
        requestData
      );
      return response;
    } catch (error) {
      console.error("Error updatePriceInOrder:", error);
      throw error;
    }
  };
  const updateDiscountProductInCart = async (
    orderId: string,
    productId: any,
    updated_by: string,
    requestData: any
  ) => {
    try {
      // const response = await $sdk.order.updateDiscountProductInCart(
      //   orderId,
      //   productId,
      //   updated_by,
      //   requestData
      // );
      // return response;
    } catch (error) {
      console.error("Error updatePriceInOrder:", error);
      throw error;
    }
  };
  const updateOrderCustomer = async (
    orderId: string,
    customerId: string,
    shippingAddress: string
  ) => {
    try {
      const response = await $sdk.order.updateCustomerAndShippingAddress(
        orderId,
        customerId,
        shippingAddress
      );
      return response;
    } catch (error) {
      console.error("Error updating customer and shipping address:", error);
      throw error;
    }
  };

  const updateDateCreateOrder = async (
    orderId: string,
    orderDate: number,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateDateCreateOrder(
        orderId,
        orderDate,
        updatedBy
      );

      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      console.error("Error updating customer and shipping address:", error);
      throw error;
    }
  };

  const createOrderReturn = async (requestData: any, created_by: any) => {
    try {
      const response = await $sdk.order.createOrderReturn(
        requestData,
        created_by
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.warning(`${response?.message}`);
      } else {
        useNuxtApp().$toast.success(`${response?.message}`);
      }
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.warning(`${error?.message}`);
      console.error("Error creating order return:", error);
      throw error;
    }
  };

  const addVoucher = async (orderId: string, voucherCode: string) => {
    const encodedVoucher = encodeURIComponent(voucherCode);

    try {
      const response = await $sdk.order.addVoucher(orderId, encodedVoucher);
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      console.error("Error addVoucher:", error);
      throw error;
    }
  };
  const removeVoucher = async (orderId: string, voucherCode: string) => {
    const encodedVoucher = encodeURIComponent(voucherCode);
    try {
      const response = await $sdk.order.removeVoucher(orderId, encodedVoucher);
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateFinancialStatus = async (
    orderId: string,
    financialStatus: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateFinancialStatus(
        orderId,
        financialStatus,
        updatedBy
      );
      return response;
    } catch (error) {
      console.error("Error updating financial status:", error);
      throw error;
    }
  };

  const completeOrder = async (orderIds: string[]) => {
    try {
      const response = await $sdk.order.completeOrder(orderIds);
      useNuxtApp().$toast.success("Hoàn thành đơn hàng thành công");
      return response;
    } catch (error) {
      console.error("Error completing order:", error);
      throw error;
    }
  };
  const openOrderById = async (orderId: string) => {
    router.push({
      path: "/sale",
      query: {
        ...router.currentRoute.value.query,
        orderId: orderId,
      },
    });
  };
  const openPaymentByOrderId = async (orderId: string) => {
    router.push({
      path: "/payment",
      query: {
        ...router.currentRoute.value.query,
        orderId: orderId,
      },
    });
  };

  const updateOrderDescription = async (
    orderId: string,
    description: string
  ) => {
    try {
      const response = await $sdk.order.updateOrderDescription(
        orderId,
        description
      );
      return response;
    } catch (error) {
      console.error("Error update order description:", error);
      throw error;
    }
  };

  const getOrderDetail = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoSellOrder(orderId);
      return response.data;
    } catch (error) {
      console.error("Error get order detail:", error);
      throw error;
    }
  };
  const createShippingInfo = async (
    ownerId: string,
    dataRequest: ShippingAddress
  ) => {
    try {
      const response = await $sdk.order.createInfoReceiver(
        ownerId,
        dataRequest
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.error("Tạo địa chỉ thất bại");
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success("Tạo địa chỉ mới thành công");
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getShippingInfo = async (ownerId: string) => {
    try {
      const response = await $sdk.order.getInfoReceiver(ownerId);
      return response;
    } catch (error) {
      console.log("🚀 ~ getShippingInfo ~ error:", error);
      throw error;
    }
  };
  const deleteShippingInfo = async (
    ownerId: string,
    receiverId: string,
    deletedBy: string
  ) => {
    try {
      const response = await $sdk.order.deleteInfoReceiver(
        ownerId,
        receiverId,
        deletedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateShippingInfo = async (
    ownerId: string,
    receiverId: string,
    updatedBy: string,
    dataRequest: ShippingAddress
  ) => {
    try {
      const response = await $sdk.order.updateInfoReceiver(
        ownerId,
        receiverId,
        updatedBy,
        dataRequest
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getOrderByIdNoLogin = async (
    partnerId: string,
    storeId: string,
    orderId: string
  ) => {
    try {
      const response = await $sdk.order.getOrderByIdNoLogin(
        partnerId,
        storeId,
        orderId
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getListShippingCarrier = async () => {
    try {
      const response = await $sdk.order.getListShippingCarrier();
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getShippingService = async (shippingCarrierId: string) => {
    try {
      const response = await $sdk.order.getShippingService(shippingCarrierId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateOrderType = async (
    orderId: string,
    orderType: string,
    updateBy: string
  ) => {
    try {
      const response = await $sdk.order.updateOrderType(
        orderId,
        orderType,
        updateBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateShippingService = async (
    orderId: string,
    shippingServiceId: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateShippingService(
        orderId,
        shippingServiceId,
        updatedBy
      );
      // useNuxtApp().$toast.success(`${response?.message}`);

      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateShippingOrder = async (orderId: string, shippingId: string) => {
    try {
      const response = await $sdk.order.updateShippingOrder(
        orderId,
        shippingId
      );
      // useNuxtApp().$toast.success(`${response?.message}`);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const searchEmployes = async (data: employeesQuery) => {
    try {
      const response = await $sdk.user.searchEmployees(data);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateWareHouseToOrder = async (
    orderId: string,
    warehouseId: string
  ) => {
    try {
      const response = await $sdk.order.updateWareHouseOrder(
        orderId,
        warehouseId
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const printOrderHTML = async (
    orderId: string,
    paymenthMethod?: string,
    paymentUrl?: string
  ) => {
    try {
      const response = await $sdk.order.printOrderHtml(
        orderId,
        paymenthMethod,
        paymentUrl
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateDiscountPriceInOrder = async (
    orderId: string,
    orderItemId: string,
    requestData: DiscountCampaign
  ) => {
    try {
      const response = await $sdk.order.updateDiscountPriceInOrder(
        orderId,
        orderItemId,
        requestData
      );
      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.error(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateDiscount = async (
    orderId: string,
    updated_by: string,
    requestData: any
  ) => {
    try {
      const response = await $sdk.order.updateDiscount(
        orderId,
        updated_by,
        requestData
      );
      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const enableProductDiary = async (orderId: string, orderItemId: string) => {
    try {
      const response = await $sdk.order.enableProductDiary(
        orderId,
        orderItemId
      );
      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.error(`${error?.message}`);
      throw error;
    }
  };
  const updateMemberDiscount = async (
    orderId: string,
    requestData: MemberDiscount
  ) => {
    try {
      const response = await $sdk.order.updateMemberDiscount(
        orderId,
        requestData
      );
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const removeMemberDiscount = async (orderId: string) => {
    try {
      const response = await $sdk.order.removeMemberDiscount(orderId);
      if (response?.status === 0) {
        useNuxtApp().$toast.error(response?.message);
      }
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const cancelOrder = async (orderId: string, requestData: any) => {
    try {
      const response = $sdk.order.updateCancelOrder(orderId, requestData);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateExchangeOrder = async (
    exchangeOrder: string,
    returnOrder: string,
    sellOrder: string
  ) => {
    try {
      const response = await $sdk.order.updateExchangeOrder(
        exchangeOrder,
        returnOrder,
        sellOrder
      );
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.warning(`${error?.message}`);
      throw error;
    }
  };

  const CreateOrderV1 = async (
    orderData: any,
    platform: string,
    createDraft: boolean,
    created_by: string
  ) => {
    try {
      const response = await $sdk.order.createOrder(
        orderData,
        platform,
        createDraft,
        created_by
      );
      console.log("đơn tạo từ", response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateRemainToTalPrice = async (orderId: string, updatedBy: string) => {
    try {
      const response = await $sdk.order.updateRemainToTalPrice(
        orderId,
        updatedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const removeShippingAddress = async (orderId: string, updateBy: string) => {
    try {
      const response = await $sdk.order.removeShippingAddress(
        orderId,
        updateBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const removeShippingInfo = async (orderId: string, updateBy: string) => {
    try {
      const response = await $sdk.order.removeShippingInfo(orderId, updateBy);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updatePriceNewInOrder = async (
    orderId: string,
    orderItemId: string,
    priceNew: number,
    reason: string
  ) => {
    try {
      const response = await updatePriceInOrder(
        orderId,
        orderItemId,
        priceNew,
        reason
      );
      if (response.status === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateVatInorder = async (
    orderId: string,
    orderItemId: string,
    vatRate: string
  ) => {
    try {
      const response = await $sdk.order.updateVatInorder(
        orderId,
        orderItemId,
        vatRate
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    searchOrderId,
    searchUser,
    searchProductId,
    paymentMethodId,
    activeTab,
    startDate,
    endDate,
    employeeId,
    fulfillment,
    currentPage,
    itemsPerPage,
    fetchListSellOrder,
    fetchListPaymentMethod,
    fetchListSaleOrderStatus,
    createOrder,
    addOrderLineItems,
    getInforSellOrder,
    removeProductInOrder,
    removeDraftOrder,
    fetchListSellOrderReturn,
    fetchListOrderRelations,
    fetchDataEmployees,
    getInfoSellOrderReturn,
    fetchShipping,
    fetchOrderNote,
    createNote,
    deleteNote,
    updateStatusApproved,
    updateStatusCancelled,
    updateStatusCompleted,
    updateStatusReturnOrderCompleted,
    updateOrderCustomer,
    createOrderReturn,
    updateFinancialStatus,
    completeOrder,
    fetchNotes,
    fetchOrderDetails,
    fetchOrderReturnDetails,
    createOrderTemp,
    updateQuantityProductInOrder,
    addVoucher,
    getOrderPromotion,
    updateShippingFee,
    updatePriceInOrder,
    updateDiscountProductInCart,
    updateDiscountCart,
    updateSaleEmployee,
    updateDateCreateOrder,
    updateShippingOrder,
    updateStatusOpen,
    getOrderDetail,
    updateOrderDescription,
    disableOrderItem,
    openOrderById,
    openPaymentByOrderId,
    createShippingInfo,
    getShippingInfo,
    deleteShippingInfo,
    updateShippingInfo,
    getOrderByIdNoLogin,
    getListShippingCarrier,
    getShippingService,
    updateOrderType,
    updateShippingService,
    createOrders,
    updateInfoCampaignPromotion,
    searchEmployes,
    updateWareHouseToOrder,
    removeVoucher,
    printOrderHTML,
    loading,
    updateDiscountPriceInOrder,
    updateDiscount,
    enableProductDiary,
    updateMemberDiscount,
    removeMemberDiscount,
    createBuyOrder,
    fetchListSellOrderAll,
    cancelOrder,
    updateExchangeOrder,
    CreateOrderV1,
    updateRemainToTalPrice,
    updateStatusReturnOrder,
    updateEmployee,
    removeShippingAddress,
    removeShippingInfo,
    updatePriceNewInOrder,
    updateVatInorder,
    updateStatusApprovedV2,
    fetchListSellOrderV2,
    fetchListSellOrderReturnV2,
  };
}
