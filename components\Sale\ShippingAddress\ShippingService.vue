<template>
  <div class="items-center text-sm">
    <div v-if="!isPageFFM" class="flex items-center gap-1 mb-2">
      <span class="text-primary">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
          />
        </svg>
      </span>
      <div class="font-semibold text-primary">Vận chuyển</div>
    </div>

    <div v-if="!isPageFFM" class="flex flex-col space-y-2">
      <!-- ĐVVC -->
      <div class="flex items-center">
        <label class="w-40">ĐVVC</label>
        <select
          v-model="selectedCarrierId"
          class="w-full outline-none border px-2 py-1 rounded bg-secondary"
          :disabled="
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
            'INVOICE_PUBLISHED'
          "
        >
          <option value="" disabled>Chọn đơn vị vận chuyển</option>
          <option
            v-for="shippingCarrier in dataListShippingCarrier"
            :key="shippingCarrier.id"
            :value="shippingCarrier.id"
          >
            {{ shippingCarrier.name }}
          </option>
        </select>
      </div>

      <!-- HTVC -->
      <div class="flex items-center">
        <label class="w-40">HTVC</label>
        <select
          v-model="selectedServiceId"
          class="w-full outline-none border px-2 py-1 rounded bg-secondary cursor-pointer"
          :disabled="
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
            'INVOICE_PUBLISHED'
          "
        >
          <option value="" disabled>Chọn hình thức vận chuyển</option>
          <option
            v-for="service in dataListShippingService"
            :key="service.id"
            :value="service.id"
          >
            {{ service.name }}
          </option>
        </select>
      </div>
    </div>
    <!-- Phí vận chuyển -->
    <div v-if="!isPageFFM" class="flex items-center mt-2">
      <div class="my-1 w-40">Phí vận chuyển</div>
      <input
        type="number"
        inputmode="numeric"
        class="w-full border bg-secondary text-base px-2 py-1 rounded font-semibold outline-none"
        v-model="shippingFee"
        :disabled="
          orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
          'INVOICE_PUBLISHED'
        "
        @blur="handleUpdateShippingFee"
      />
    </div>
    <!-- pageFFM -->
    <div v-if="isPageFFM" class="flex items-center justify-center gap-2">
      <div class="w-1/3">
        <label class="w-40">Đơn vị vận chuyển</label>
        <select
          v-model="selectedCarrierId"
          class="w-full outline-none border px-2 py-[6px] rounded bg-secondary cursor-pointer"
        >
          <option value="" disabled>Chọn đơn vị vận chuyển</option>
          <option
            v-for="shippingCarrier in dataListShippingCarrier"
            :key="shippingCarrier.id"
            :value="shippingCarrier.id"
          >
            {{ shippingCarrier.name }}
          </option>
        </select>
      </div>

      <!-- HTVC -->
      <div class="w-1/3">
        <label class="w-40">Hình thức vận chuyển</label>
        <select
          v-model="selectedServiceId"
          class="w-full outline-none border px-2 py-[6px] rounded bg-secondary cursor-pointer"
        >
          <option value="" disabled>Chọn hình thức vận chuyển</option>
          <option
            v-for="service in dataListShippingService"
            :key="service.id"
            :value="service.id"
          >
            {{ service.name }}
          </option>
        </select>
      </div>
      <div class="w-1/3">
        <div class="w-40">Phí vận chuyển</div>
        <input
          type="number"
          inputmode="numeric"
          class="w-full border bg-secondary text-base px-2 py-1 rounded font-semibold outline-none"
          v-model="shippingFee"
          @blur="handleUpdateShippingFee"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const orderStore = useOrderStore();
const props = defineProps(["orderDetail", "isPageFFM"]);
const selectedCarrierId = ref<string>(
  props.orderDetail?.order?.customAttribute?.carrierId || ""
);

const selectedServiceId = ref<string>(
  props.orderDetail?.order?.customAttribute?.shippingServiceId || ""
);

const shippingFee = ref(
  props.orderDetail?.order?.totalShippingPrice?.amount || 0
);
const {
  getListShippingCarrier,
  getShippingService,
  updateShippingService,
  updateShippingFee,
  updateShippingOrder,
} = useOrder();
const dataListShippingCarrier = ref<any>([]);
const dataListShippingService = ref<any>([]);
const handleGetShippingCarrier = async () => {
  try {
    const response = await getListShippingCarrier();
    dataListShippingCarrier.value = response?.data;
    return response;
  } catch (error) {
    throw error;
  }
};
const handleGetShippingService = async (shippingCarrier: string) => {
  try {
    const response = await getShippingService(shippingCarrier);
    dataListShippingService.value = response?.data;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetShippingCarrier();
  if (selectedCarrierId.value) {
    await handleGetShippingService(selectedCarrierId.value);
  }
});
const isProgrammaticUpdate = ref(false);

watch(selectedCarrierId, async (newVal: string) => {
  if (isProgrammaticUpdate.value) return; // không chạy nếu đang cập nhật từ code

  if (newVal) {
    await handleGetShippingService(newVal);
    orderStore.idShippingCarrier = newVal;
    orderStore.idShippingService = "";
    selectedServiceId.value = "";
    await updateShippingOrder(props.orderDetail?.id, newVal);
    // await orderStore.updateOrder(props.orderDetail?.id);
  }
});
watch(selectedServiceId, async (newVal: string) => {
  if (isProgrammaticUpdate.value) return; // không chạy nếu đang cập nhật từ code

  if (newVal) {
    orderStore.idShippingService = newVal;
    await updateShippingService(props.orderDetail?.id, newVal, "");
    // await orderStore.updateOrder(props.orderDetail?.id);
  }
});
// idShippingCarrier,
// idShippingService,
// Cập nhật lại giá trị khi props thay đổi (do dùng ref -> chỉ cập nhật khi mounted)
watch(
  () => props.orderDetail,
  async (newVal) => {
    isProgrammaticUpdate.value = true;
    //
    orderStore.idShippingCarrier =
      newVal?.order?.customAttribute?.carrierId || "";
    orderStore.idShippingService =
      newVal?.order?.customAttribute?.shippingServiceId || "";
    //
    selectedCarrierId.value = newVal?.order?.customAttribute?.carrierId || "";
    selectedServiceId.value =
      newVal?.order?.customAttribute?.shippingServiceId || "";
    shippingFee.value = newVal?.order?.totalShippingPrice?.amount || 0;

    if (selectedCarrierId.value) {
      await handleGetShippingService(selectedCarrierId.value);
    }
    isProgrammaticUpdate.value = false;
  },
  { deep: true }
);

const handleUpdateShippingFee = async () => {
  if (
    props.orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  await updateShippingFee(props.orderDetail?.id, shippingFee.value);
  // await orderStore.updateOrder(props.orderDetail?.id);
};
</script>
