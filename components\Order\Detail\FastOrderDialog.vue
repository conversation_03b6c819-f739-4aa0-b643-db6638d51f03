<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-gray-100 rounded-lg shadow-lg max-w-7xl h-[85vh] w-full animate-popup"
    >
      <!-- Header -->
      <div
        class="bg-primary text-white px-4 py-2 rounded-t-lg flex items-center justify-between"
      >
        <div class="flex items-center gap-3">
          <div
            class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              class="w-5 h-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
              />
            </svg>
          </div>
          <div>
            <h2 class="text-xl font-bold">Chi tiết đơn hàng</h2>
            <p class="text-white/80 text-sm">
              {{ order?.id ? `#${order.id}` : "Đang tải..." }}
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <!-- Order Status Badge -->
          <div
            v-if="order?.order?.status"
            class="px-3 py-1 bg-white/20 rounded-full"
          >
            <span class="text-sm font-medium">
              {{ getOrderStatusText(order.order.status) }}
            </span>
          </div>
          <div class="flex items-center gap-3">
            <button
              @click="handleNavigate"
              class="flex items-center justify-center bg-white/20 p-2 hover:bg-white/30 rounded transition-colors duration-200"
              title="Chỉnh sửa đơn hàng"
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                  />
                </svg>
              </span>
              <span>Chỉnh sửa</span>
            </button>
            <!-- Close Button -->
            <button
              @click="cancel"
              class="flex items-center justify-center w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
              title="Đóng"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                class="w-5 h-5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div
        class="p-2 space-y-2 overflow-y-auto"
        style="height: calc(85vh - 80px)"
      >
        <div class="grid-cols-12 grid space-x-2">
          <FastEmployee class="col-span-6" :order="order"></FastEmployee>
          <FastNote class="col-span-3" :order="order"> </FastNote>
          <FastCustomer class="col-span-3" :order="order"></FastCustomer>
        </div>
        <div class="grid-cols-12 grid space-x-2">
          <FastListProduct class="col-span-9" :order="order"></FastListProduct>
          <FastPayment class="col-span-3" :order="order"> </FastPayment>
        </div>
        <div class="grid-cols-12 grid space-x-2">
          <FastHistoryPayment
            class="col-span-9"
            :order="order"
          ></FastHistoryPayment>
          <FastRelativeOrder class="col-span-3" :order="order">
          </FastRelativeOrder>
        </div>
      </div>
      <StorePopup
        v-if="isAlert"
        @confirm="handleContinue"
        @cancel="handleContinueCurrentStore"
      ></StorePopup>
    </div>
  </div>
</template>

<script setup>
const emits = defineEmits(["cancel"]);
const props = defineProps(["order"]);
const cancel = () => {
  emits("cancel");
};
const { setStore } = usePermission();
const router = useRouter();
const { storeId, subStoreId, orgId } = useTabContext();
const isAlert = ref(false);
async function handleNavigate() {
  const { storeId, orgId, subStoreId } = useTabContext();
  if (storeId.value !== subStoreId.value) {
    isAlert.value = true;
    return;
  }
  let isTimeout = false;
  const timer = setTimeout(() => {
    isTimeout = true;
    loadingNavigate.value = true;
  }, 150);

  await router.push(
    `/sale?orderId=${props.order?.id}&orgId=${orgId.value}&storeId=${storeId.value}`
  );
  clearTimeout(timer);

  if (isTimeout) loadingNavigate.value = false;
}
const handleContinue = async () => {
  let isTimeout = false;
  await setStore(subStoreId.value);
  router.push({
    query: {
      ...router.currentRoute.value.query,
      storeId: subStoreId.value,
    },
  });
  nextTick();

  const timer = setTimeout(() => {
    isTimeout = true;
    loadingNavigate.value = true;
  }, 150);

  await router.push(
    `/sale?orderId=${props.order?.id}&orgId=${orgId.value}&storeId=${subStoreId.value}`
  );
  clearTimeout(timer);

  if (isTimeout) loadingNavigate.value = false;
};
const handleContinueCurrentStore = async () => {
  isAlert.value = false;
};
</script>
