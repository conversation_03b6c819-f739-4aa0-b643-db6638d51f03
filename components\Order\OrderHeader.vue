<template>
  <div class="order-header-container ">
    <div v-if="isMobile && orderDetail" class="block md:hidden">
      <div class="flex items-center border-b pb-1 py-2">
        <div class="mt-1">
          <div class="flex items-center justify-center gap-2">
            <!-- Order ID -->
            <span class="text-primary font-semibold text-sm">
              #{{ orderDetail?.id }}
            </span>
            
            <!-- Order Status -->
            <span :class="getOrderStatusClass(orderDetail?.status)" class="text-xs">
              {{ orderDetail?.statusDescription }}
            </span>
            
            <!-- FFM Status -->
            <div
              v-if="orderDetail?.order?.fulfillmentStatus"
              class="flex items-center justify-center gap-1"
            >
              <span class="text-gray-400">-</span>
              <svg
                class="w-3.5 h-3.5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
                />
              </svg>
              <span :class="getFFMStatusClass(orderDetail?.order?.fulfillmentStatus)" class="text-xs">
                {{ getFFMStatusText(orderDetail?.order?.fulfillmentStatus) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex items-center gap-1 px-2 pb-2">
        <ActionButtonOrder :diary="orderDetail" />
      </div>
    </div>

    <div v-if="!isMobile && orderDetail" class="hidden md:block border-b py-1">
      <div class="px-2">
        <div class="flex items-center justify-between">
          <div class="text-primary font-semibold text-sm">
            #{{ orderDetail?.id }}
          </div>
          
          <div class="flex items-center justify-center">
            <div class="flex items-center" v-tippy="'Trạng thái đơn'">
              <span :class="getOrderStatusClass(orderDetail?.status)" class="text-xs">
                {{ orderDetail?.statusDescription }}
              </span>
              
              <button
                v-if="orderDetail?.status === 'CANCELLED'"
                v-tippy="`${orderDetail?.order?.note}`"
                class="ml-1 text-gray-500 hover:text-gray-700 focus:outline-none"
                type="button"
                aria-label="Thông tin hủy đơn"
              >
                <svg
                  class="w-3.5 h-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                  />
                </svg>
              </button>
              
              <div v-if="orderDetail?.order?.customAttribute?.subType" class="ml-2">
                <span
                  v-if="orderDetail?.order?.customAttribute?.subType === 'RETURN_FULL'"
                  class="text-xs font-semibold text-red-500"
                >
                  - Trả toàn bộ
                </span>
                <span
                  v-else-if="orderDetail?.order?.customAttribute?.subType === 'EXCHANGE_ORDER'"
                  class="text-xs font-semibold text-primary"
                >
                  - Đơn đổi
                </span>
                <span v-else class="text-xs font-semibold text-orange-400">
                  - Trả một phần
                </span>
              </div>
            </div>
            
            <div
              v-if="orderDetail?.order?.fulfillmentStatus"
              class="flex items-center justify-center gap-1 ml-2"
              v-tippy="'Trạng thái FFM'"
            >
              <span class="text-gray-400">-</span>
              <svg
                class="w-3.5 h-3.5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1.5"
                  d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
                />
              </svg>
              <span :class="getFFMStatusClass(orderDetail?.order?.fulfillmentStatus)" class="text-xs">
                {{ getFFMStatusText(orderDetail?.order?.fulfillmentStatus) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div
        v-if="orderDetail?.order?.customAttribute?.exportVatInvoiceStatus === 'INVOICE_PUBLISHED'"
        class="flex items-center gap-2 px-2 text-xs mt-2"
      >
        <span class="text-gray-600">Số hóa đơn:</span>
        <span class="font-medium">{{ invoice?.value }}</span>
        <button
          @click="$emit('toggleExportInvoice')"
          class="text-primary underline hover:text-primary-dark focus:outline-none focus:ring-1 focus:ring-primary rounded transition-colors duration-200"
          type="button"
        >
          (Xem thông tin)
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface OrderDetail {
  id: string;
  status: string;
  statusDescription: string;
  order?: {
    fulfillmentStatus?: string;
    note?: string;
    customAttribute?: {
      subType?: string;
      exportVatInvoiceStatus?: string;
    };
  };
}

interface Invoice {
  value: string;
}

interface Props {
  orderDetail?: OrderDetail;
  invoice?: Invoice;
  isMobile?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false,
});

const emit = defineEmits<{
  toggleExportInvoice: [];
}>();

const ActionButtonOrder = defineAsyncComponent({
  loader: () => import("~/components/Order/ActionButtonOrder.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () => h("div", { class: "animate-pulse bg-gray-200 h-8 w-20 rounded" }),
  errorComponent: () => h("div", { class: "text-red-500 text-xs" }, "Failed to load actions"),
});

import {
  getOrderStatusClass,
  getFFMStatusClass,
  getFFMStatusText,
} from "~/utils/statusHelpers";

import "tippy.js/dist/tippy.css";
</script>

<style scoped>

.order-header-container {
  transition: all 0.2s ease-in-out;
}

.status-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

@media (max-width: 640px) {
  .order-header-container {
    padding: 0.25rem;
  }
}

@media (prefers-color-scheme: dark) {
  .order-header-container {
    background-color: rgb(31 41 55);
    color: rgb(243 244 246);
  }
}

@media (prefers-reduced-motion: reduce) {
  .order-header-container,
  button {
    transition: none;
  }
  
  button:hover {
    transform: none;
  }
}

.tippy-box {
  background-color: rgb(31 41 55);
  color: rgb(243 244 246);
  border-radius: 0.5rem;
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

.tippy-arrow {
  color: rgb(31 41 55);
}
</style>
